/**
 * Accessibility Testing Component for Development
 * Comprehensive accessibility testing and monitoring dashboard
 */

import React, { useState, useEffect } from 'react';
import { 
  liveRegionManager, 
  a11yTesting, 
  highContrastUtils, 
  fontSizeUtils,
  contrastUtils,
  screenReaderUtils 
} from '../../utils/accessibility';

const AccessibilityTester = ({ isVisible = false, onClose }) => {
  const [testResults, setTestResults] = useState([]);
  const [isHighContrast, setIsHighContrast] = useState(false);
  const [fontScale, setFontScale] = useState(1);
  const [isRunningTests, setIsRunningTests] = useState(false);

  useEffect(() => {
    if (!isVisible) return;

    // Initialize current states
    setIsHighContrast(document.documentElement.classList.contains('high-contrast'));
    setFontScale(fontSizeUtils.getCurrentScale());

    // Run initial accessibility audit
    runAccessibilityTests();
  }, [isVisible]);

  const runAccessibilityTests = async () => {
    setIsRunningTests(true);
    
    try {
      const results = [];

      // Basic accessibility audit
      const basicIssues = a11yTesting.auditPage();
      results.push({
        category: 'Basic Accessibility',
        issues: basicIssues,
        status: basicIssues.length === 0 ? 'pass' : 'fail'
      });

      // Keyboard navigation test
      const keyboardIssues = testKeyboardNavigation();
      results.push({
        category: 'Keyboard Navigation',
        issues: keyboardIssues,
        status: keyboardIssues.length === 0 ? 'pass' : 'warning'
      });

      // Color contrast test
      const contrastIssues = testColorContrast();
      results.push({
        category: 'Color Contrast',
        issues: contrastIssues,
        status: contrastIssues.length === 0 ? 'pass' : 'fail'
      });

      // ARIA attributes test
      const ariaIssues = testAriaAttributes();
      results.push({
        category: 'ARIA Attributes',
        issues: ariaIssues,
        status: ariaIssues.length === 0 ? 'pass' : 'warning'
      });

      // Semantic HTML test
      const semanticIssues = testSemanticHTML();
      results.push({
        category: 'Semantic HTML',
        issues: semanticIssues,
        status: semanticIssues.length === 0 ? 'pass' : 'warning'
      });

      setTestResults(results);
    } catch (error) {
      console.error('Error running accessibility tests:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  const testKeyboardNavigation = () => {
    const issues = [];
    
    // Check for focusable elements without visible focus indicators
    const focusableElements = document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    let elementsWithoutFocus = 0;
    focusableElements.forEach(element => {
      const style = window.getComputedStyle(element, ':focus');
      if (!style.outline || style.outline === 'none') {
        elementsWithoutFocus++;
      }
    });

    if (elementsWithoutFocus > 0) {
      issues.push(`${elementsWithoutFocus} focusable elements without visible focus indicators`);
    }

    // Check for skip links
    const skipLinks = document.querySelectorAll('.skip-link, [href^="#"]');
    if (skipLinks.length === 0) {
      issues.push('No skip links found for keyboard navigation');
    }

    return issues;
  };

  const testColorContrast = () => {
    const issues = [];
    
    // Sample some text elements for contrast testing
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a, button');
    let lowContrastCount = 0;

    Array.from(textElements).slice(0, 50).forEach(element => {
      const style = window.getComputedStyle(element);
      const color = style.color;
      const backgroundColor = style.backgroundColor;
      
      // Parse RGB values (simplified)
      const colorMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
      const bgMatch = backgroundColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
      
      if (colorMatch && bgMatch) {
        const textColor = [parseInt(colorMatch[1]), parseInt(colorMatch[2]), parseInt(colorMatch[3])];
        const bgColor = [parseInt(bgMatch[1]), parseInt(bgMatch[2]), parseInt(bgMatch[3])];
        
        if (!contrastUtils.meetsWCAG(textColor, bgColor, 'AA', 'normal')) {
          lowContrastCount++;
        }
      }
    });

    if (lowContrastCount > 0) {
      issues.push(`${lowContrastCount} elements with potentially low color contrast`);
    }

    return issues;
  };

  const testAriaAttributes = () => {
    const issues = [];

    // Check for buttons without aria-label or text content
    const buttons = document.querySelectorAll('button');
    let buttonsWithoutLabels = 0;
    
    buttons.forEach(button => {
      const hasText = button.textContent.trim().length > 0;
      const hasAriaLabel = button.getAttribute('aria-label');
      const hasAriaLabelledBy = button.getAttribute('aria-labelledby');
      
      if (!hasText && !hasAriaLabel && !hasAriaLabelledBy) {
        buttonsWithoutLabels++;
      }
    });

    if (buttonsWithoutLabels > 0) {
      issues.push(`${buttonsWithoutLabels} buttons without accessible labels`);
    }

    // Check for form controls without labels
    const formControls = document.querySelectorAll('input, select, textarea');
    let controlsWithoutLabels = 0;
    
    formControls.forEach(control => {
      const hasLabel = document.querySelector(`label[for="${control.id}"]`);
      const hasAriaLabel = control.getAttribute('aria-label');
      const hasAriaLabelledBy = control.getAttribute('aria-labelledby');
      
      if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
        controlsWithoutLabels++;
      }
    });

    if (controlsWithoutLabels > 0) {
      issues.push(`${controlsWithoutLabels} form controls without labels`);
    }

    return issues;
  };

  const testSemanticHTML = () => {
    const issues = [];

    // Check for proper heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length === 0) {
      issues.push('No heading elements found');
    } else {
      const h1Count = document.querySelectorAll('h1').length;
      if (h1Count === 0) {
        issues.push('No h1 element found');
      } else if (h1Count > 1) {
        issues.push('Multiple h1 elements found');
      }
    }

    // Check for main landmark
    const main = document.querySelector('main, [role="main"]');
    if (!main) {
      issues.push('No main landmark found');
    }

    // Check for navigation landmark
    const nav = document.querySelector('nav, [role="navigation"]');
    if (!nav) {
      issues.push('No navigation landmark found');
    }

    return issues;
  };

  const toggleHighContrast = () => {
    const newState = highContrastUtils.toggleHighContrast();
    setIsHighContrast(newState);
  };

  const adjustFontSize = (direction) => {
    let newScale;
    if (direction === 'increase') {
      newScale = fontSizeUtils.increaseFontSize();
    } else if (direction === 'decrease') {
      newScale = fontSizeUtils.decreaseFontSize();
    } else {
      newScale = fontSizeUtils.resetFontSize();
    }
    setFontScale(newScale);
  };

  const testScreenReader = () => {
    liveRegionManager.announce('Testing screen reader announcement. This message should be read aloud by assistive technologies.');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 left-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 max-w-lg max-h-96 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Accessibility Tester
        </h3>
        <button
          onClick={onClose}
          className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
          aria-label="Close accessibility tester"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Controls */}
      <div className="p-4 space-y-4">
        {/* Test Controls */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Accessibility Tests
          </h4>
          <button
            onClick={runAccessibilityTests}
            disabled={isRunningTests}
            className="w-full px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {isRunningTests ? 'Running Tests...' : 'Run Accessibility Audit'}
          </button>
        </div>

        {/* Accessibility Controls */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Accessibility Controls
          </h4>
          <div className="space-y-2">
            <button
              onClick={toggleHighContrast}
              className={`w-full px-3 py-2 text-sm rounded ${
                isHighContrast 
                  ? 'bg-yellow-600 text-white' 
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              {isHighContrast ? 'Disable' : 'Enable'} High Contrast
            </button>
            
            <div className="flex space-x-1">
              <button
                onClick={() => adjustFontSize('decrease')}
                className="flex-1 px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                A-
              </button>
              <button
                onClick={() => adjustFontSize('reset')}
                className="flex-1 px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                A
              </button>
              <button
                onClick={() => adjustFontSize('increase')}
                className="flex-1 px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                A+
              </button>
            </div>
            
            <div className="text-xs text-gray-500">
              Font Scale: {Math.round(fontScale * 100)}%
            </div>
            
            <button
              onClick={testScreenReader}
              className="w-full px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700"
            >
              Test Screen Reader
            </button>
          </div>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Test Results
            </h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="p-2 rounded border">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{result.category}</span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      result.status === 'pass' 
                        ? 'bg-green-100 text-green-800' 
                        : result.status === 'warning'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                  {result.issues.length > 0 && (
                    <ul className="mt-1 text-xs text-gray-600 space-y-1">
                      {result.issues.map((issue, issueIndex) => (
                        <li key={issueIndex}>• {issue}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Accessibility testing toggle component
export const AccessibilityToggle = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleKeyPress = (e) => {
      // Ctrl+Shift+A to toggle accessibility tester
      if (e.ctrlKey && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-20 right-4 z-40 p-3 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-colors"
        title="Accessibility Tester (Ctrl+Shift+A)"
        aria-label="Open accessibility testing dashboard"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      </button>

      {/* Tester */}
      <AccessibilityTester
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
      />
    </>
  );
};

export default AccessibilityTester;
