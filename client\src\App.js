import React, { useEffect, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import './styles/tab-specific.css';
import './styles/accessibility.css';
import { useAuth } from './context/AuthContext';
import { ThemeProvider } from './context/ThemeContext';
import { initAccessibility } from './utils/accessibility';
import { globalCleanup } from './utils/requestOptimizer';
import { performanceMonitor, LazyLoadingUtils } from './utils/performanceOptimization';
import { gsapAnimations } from './utils/gsapAnimations';

// Critical pages (loaded immediately)
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import ModernRegisterPage from './pages/ModernRegisterPage';

// Lazy-loaded pages for better performance
import {
  LazyDashboardPage,
  LazyCheckLinkPage,
  LazyCommunityPage,
  LazyProfilePage,
  LazySettingsPage,
  createLazyRoute,
  preloadOnHover
} from './components/LazyComponents';

// Lazy load remaining pages
const LazyRegistrationSuccessPage = createLazyRoute(() => import('./pages/RegistrationSuccessPage'));
const LazyChatPage = createLazyRoute(() => import('./pages/ChatPage'));
const LazyAdminDashboard = createLazyRoute(() => import('./pages/AdminDashboard'));
const LazyCommunityFeedPage = createLazyRoute(() => import('./pages/CommunityFeedPage'));
const LazySubmitArticlePage = createLazyRoute(() => import('./pages/SubmitArticlePage'));
const LazyKnowledgeBasePage = createLazyRoute(() => import('./pages/KnowledgeBasePage'));
const LazyMySubmissionsPage = createLazyRoute(() => import('./pages/MySubmissionsPage'));
const LazyVerifyEmailPage = createLazyRoute(() => import('./pages/VerifyEmailPage'));
const LazyEmailVerificationRequiredPage = createLazyRoute(() => import('./pages/EmailVerificationRequiredPage'));
const LazyForgotPasswordPage = createLazyRoute(() => import('./pages/ForgotPasswordPage'));
const LazyResetPasswordPage = createLazyRoute(() => import('./pages/ResetPasswordPage'));
const LazySecurityPage = createLazyRoute(() => import('./pages/SecurityPage'));
const LazyAnalyticsPage = createLazyRoute(() => import('./pages/AnalyticsPage'));
const LazyPremiumPage = createLazyRoute(() => import('./pages/PremiumPage'));
const LazyNotificationsPage = createLazyRoute(() => import('./pages/NotificationsPage'));
const LazyFavoritesPage = createLazyRoute(() => import('./pages/FavoritesPage'));
const LazyAchievementsPage = createLazyRoute(() => import('./pages/AchievementsPage'));
const LazyHelpPage = createLazyRoute(() => import('./pages/HelpPage'));
const LazyFirestoreTestPanel = createLazyRoute(() => import('./components/admin/FirestoreTestPanel'));

// Components - organized imports
import ProtectedRoute from './components/auth/ProtectedRoute';
import EmailVerifiedRoute from './components/auth/EmailVerifiedRoute';
import { LoadingSpinner, ErrorBoundary } from './components/common';
import NavigationLayout from './components/navigation/NavigationLayout';
import GlobalAnimationProvider from './components/animations/GlobalAnimationProvider';
import { PerformanceToggle } from './components/dev/PerformanceDashboard';
import { AccessibilityToggle } from './components/dev/AccessibilityTester';

function App() {
  const { user, loading } = useAuth();

  // Initialize performance monitoring and optimizations
  useEffect(() => {
    // Initialize accessibility features
    initAccessibility();

    // Initialize GSAP animations
    gsapAnimations.init();

    // Initialize lazy loading for images
    LazyLoadingUtils.lazyLoadImages();

    // Preload critical resources
    LazyLoadingUtils.preloadResource('/fonts/inter.woff2', 'font', 'font/woff2');

    // Initialize performance monitoring
    console.log('🚀 Performance monitoring initialized');

    // Cleanup function to prevent request overload
    return () => {
      globalCleanup();
      performanceMonitor.cleanup();
      gsapAnimations.cleanup();
    };
  }, []);

  // Preload components based on user authentication state
  useEffect(() => {
    if (user) {
      // Preload dashboard and other authenticated routes
      import('./pages/DashboardPage');
      import('./pages/CommunityPage');
      import('./pages/CheckLinkPage');
    }
  }, [user]);

  if (loading) {
    return <LoadingSpinner />;
  }  return (
    <ErrorBoundary>
      <ThemeProvider>
        <GlobalAnimationProvider>
          <NavigationLayout>
          <Routes>
            {/* Public routes */}
            <Route
              path="/"
              element={<HomePage />}
            />
            <Route
              path="/login"
              element={user ? <Navigate to="/dashboard" /> : <LoginPage />}
            />
            <Route
              path="/register"
                element={user ? <Navigate to="/dashboard" /> : <ModernRegisterPage />}
              />

              <Route path="/registration-success" element={<LazyRegistrationSuccessPage />} />
              <Route path="/verify-email" element={<LazyVerifyEmailPage />} />
              <Route path="/email-verification-required" element={<LazyEmailVerificationRequiredPage />} />
              <Route path="/forgot-password" element={<LazyForgotPasswordPage />} />
              <Route path="/reset-password" element={<LazyResetPasswordPage />} />

          {/* Protected routes with lazy loading */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <LazyDashboardPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/check"
            element={
              <EmailVerifiedRoute>
                <LazyCheckLinkPage />
              </EmailVerifiedRoute>
            }
          />
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <LazyProfilePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/chat"
            element={
              <ProtectedRoute>
                <EmailVerifiedRoute>
                  <LazyChatPage />
                </EmailVerifiedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <LazyAdminDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/community"
            element={
              <ProtectedRoute>
                <LazyCommunityPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/community/feed"
            element={
              <ProtectedRoute>
                <LazyCommunityFeedPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/submit"
            element={
              <ProtectedRoute>
                <EmailVerifiedRoute>
                  <LazySubmitArticlePage />
                </EmailVerifiedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/my-submissions"
            element={
              <ProtectedRoute>
                <EmailVerifiedRoute>
                  <LazyMySubmissionsPage />
                </EmailVerifiedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/knowledge"
            element={<LazyKnowledgeBasePage />}
          />
          <Route
            path="/settings"
            element={
              <ProtectedRoute>
                <LazySettingsPage />
              </ProtectedRoute>
            }
          />

          {/* New feature pages with lazy loading */}
          <Route path="/security" element={<LazySecurityPage />} />
          <Route path="/analytics" element={<LazyAnalyticsPage />} />
          <Route path="/premium" element={<LazyPremiumPage />} />
          <Route
            path="/notifications"
            element={
              <ProtectedRoute>
                <LazyNotificationsPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/favorites"
            element={
              <ProtectedRoute>
                <LazyFavoritesPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/achievements"
            element={
              <ProtectedRoute>
                <LazyAchievementsPage />
              </ProtectedRoute>
            }
          />
          <Route path="/help" element={<LazyHelpPage />} />

          {/* Admin/Test Routes */}
          <Route
            path="/admin/firestore-test"
            element={
              <ProtectedRoute>
                <LazyFirestoreTestPanel />
              </ProtectedRoute>
            }
          />
          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
          </NavigationLayout>

          {/* Development tools (development only) */}
          <PerformanceToggle />
          <AccessibilityToggle />
        </GlobalAnimationProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
