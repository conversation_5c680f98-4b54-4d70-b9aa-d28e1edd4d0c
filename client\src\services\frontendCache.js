/**
 * Frontend In-Memory Cache Service
 * Optimized caching for homepage statistics and recent activities
 */

class FrontendCacheService {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 50; // Limit cache size
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
    
    // Different TTL for different data types
    this.cacheTTL = {
      stats: 10 * 60 * 1000,      // 10 minutes for stats
      trending: 3 * 60 * 1000,    // 3 minutes for trending
      community: 5 * 60 * 1000,   // 5 minutes for community
      recent: 2 * 60 * 1000,      // 2 minutes for recent activities
      user: 15 * 60 * 1000        // 15 minutes for user data
    };

    // Fallback data for when API fails
    this.fallbackData = {
      stats: {
        totalUsers: 1247,
        totalChecks: 8934,
        accuracy: 94,
        trends: 156
      },
      trending: [],
      community: [],
      recent: []
    };

    // Auto cleanup expired cache every 5 minutes
    this.startCleanupInterval();
  }

  /**
   * Generate cache key
   */
  generateKey(type, params = {}) {
    const paramStr = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    return paramStr ? `${type}_${paramStr}` : type;
  }

  /**
   * Check if cache item is valid
   */
  isValidCache(cacheItem) {
    if (!cacheItem) return false;
    return Date.now() - cacheItem.timestamp < cacheItem.ttl;
  }

  /**
   * Get from cache
   */
  get(type, params = {}) {
    const key = this.generateKey(type, params);
    const cacheItem = this.cache.get(key);
    
    if (this.isValidCache(cacheItem)) {
      console.log(`📦 Frontend cache hit: ${key}`);
      return cacheItem.data;
    }
    
    console.log(`❌ Frontend cache miss: ${key}`);
    return null;
  }

  /**
   * Set cache with TTL
   */
  set(type, params = {}, data, customTTL = null) {
    const key = this.generateKey(type, params);
    const ttl = customTTL || this.cacheTTL[type] || this.defaultTTL;
    
    // Implement LRU cache
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
      console.log(`🗑️ Evicted cache: ${firstKey}`);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
    
    console.log(`💾 Frontend cached: ${key} (TTL: ${ttl}ms)`);
  }

  /**
   * Get fallback data when API fails
   */
  getFallback(type) {
    console.log(`🔄 Using fallback data for: ${type}`);
    return this.fallbackData[type] || null;
  }

  /**
   * Clear specific cache or all cache
   */
  clear(type = null) {
    if (type) {
      const keysToDelete = [];
      for (const key of this.cache.keys()) {
        if (key.startsWith(type)) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => this.cache.delete(key));
      console.log(`🧹 Cleared cache for type: ${type}`);
    } else {
      this.cache.clear();
      console.log('🧹 Cleared all frontend cache');
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let validItems = 0;
    let expiredItems = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp < item.ttl) {
        validItems++;
      } else {
        expiredItems++;
      }
    }

    return {
      totalItems: this.cache.size,
      validItems,
      expiredItems,
      maxSize: this.maxCacheSize,
      hitRate: this.hitRate || 0
    };
  }

  /**
   * Auto cleanup expired items
   */
  startCleanupInterval() {
    setInterval(() => {
      const now = Date.now();
      const keysToDelete = [];

      for (const [key, item] of this.cache.entries()) {
        if (now - item.timestamp >= item.ttl) {
          keysToDelete.push(key);
        }
      }

      keysToDelete.forEach(key => this.cache.delete(key));
      
      if (keysToDelete.length > 0) {
        console.log(`🧹 Auto-cleaned ${keysToDelete.length} expired cache items`);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Optimized fetch with cache and fallback
   */
  async fetchWithCache(type, fetchFunction, params = {}, options = {}) {
    const { 
      useCache = true, 
      useFallback = true, 
      timeout = 5000,
      retries = 1 
    } = options;

    // Try cache first
    if (useCache) {
      const cached = this.get(type, params);
      if (cached) {
        return cached;
      }
    }

    // Try API with timeout and retries
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const result = await fetchFunction({
          signal: controller.signal,
          ...params
        });

        clearTimeout(timeoutId);

        if (result) {
          // Cache successful result
          this.set(type, params, result);
          return result;
        }
      } catch (error) {
        console.warn(`Attempt ${attempt + 1} failed for ${type}:`, error.message);
        
        if (attempt === retries) {
          // Last attempt failed, use fallback if available
          if (useFallback) {
            const fallback = this.getFallback(type);
            if (fallback) {
              return fallback;
            }
          }
          throw error;
        }
      }
    }
  }

  /**
   * Preload critical data
   */
  async preloadCriticalData() {
    console.log('🚀 Preloading critical homepage data...');
    
    const preloadTasks = [
      this.preloadStats(),
      this.preloadTrending(),
      this.preloadCommunity()
    ];

    try {
      await Promise.allSettled(preloadTasks);
      console.log('✅ Critical data preloaded');
    } catch (error) {
      console.warn('⚠️ Some preload tasks failed:', error);
    }
  }

  async preloadStats() {
    try {
      const response = await fetch('/api/community/stats');
      if (response.ok) {
        const data = await response.json();
        this.set('stats', {}, data.data);
      }
    } catch (error) {
      console.warn('Stats preload failed:', error);
    }
  }

  async preloadTrending() {
    try {
      const response = await fetch('/api/community/trending-posts?limit=5');
      if (response.ok) {
        const data = await response.json();
        this.set('trending', { limit: 5 }, data.data);
      }
    } catch (error) {
      console.warn('Trending preload failed:', error);
    }
  }

  async preloadCommunity() {
    try {
      const response = await fetch('/api/community/posts?limit=4');
      if (response.ok) {
        const data = await response.json();
        this.set('community', { limit: 4 }, data.data);
      }
    } catch (error) {
      console.warn('Community preload failed:', error);
    }
  }
}

// Create singleton instance
const frontendCache = new FrontendCacheService();

export default frontendCache;
