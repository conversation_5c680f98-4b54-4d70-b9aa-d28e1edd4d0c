/**
 * Enhanced Loading Skeleton Components
 * Provides better loading states for homepage components
 */

import React from 'react';
import { useTheme } from '../../context/ThemeContext';

// Base skeleton component
export const Skeleton = ({ className = '', width = 'w-full', height = 'h-4' }) => {
  const { isDarkMode } = useTheme();
  
  return (
    <div 
      className={`animate-pulse ${width} ${height} rounded ${
        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
      } ${className}`}
    />
  );
};

// Card skeleton for trending articles
export const TrendingCardSkeleton = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <div className={`p-5 rounded-xl border ${
      isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
    }`}>
      <div className="flex items-start space-x-4">
        {/* Rank badge */}
        <Skeleton width="w-8" height="h-8" className="rounded-full" />
        
        <div className="flex-1 space-y-3">
          {/* Title */}
          <Skeleton width="w-full" height="h-5" />
          <Skeleton width="w-3/4" height="h-4" />
          
          {/* Credibility score */}
          <Skeleton width="w-32" height="h-6" className="rounded-full" />
          
          {/* Stats */}
          <div className="flex items-center space-x-4">
            <Skeleton width="w-12" height="h-3" />
            <Skeleton width="w-16" height="h-3" />
            <Skeleton width="w-14" height="h-3" />
            <Skeleton width="w-20" height="h-3" />
          </div>
          
          {/* Author */}
          <Skeleton width="w-24" height="h-3" />
        </div>
        
        {/* External link icon */}
        <Skeleton width="w-5" height="h-5" />
      </div>
    </div>
  );
};

// Community post skeleton
export const CommunityPostSkeleton = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <div className={`flex space-x-4 p-4 rounded-xl border ${
      isDarkMode ? 'border-gray-700' : 'border-gray-100'
    }`}>
      {/* Post type icon */}
      <Skeleton width="w-12" height="h-12" className="rounded-lg flex-shrink-0" />
      
      <div className="flex-1 space-y-2">
        {/* Title */}
        <Skeleton width="w-full" height="h-4" />
        
        {/* Content */}
        <Skeleton width="w-5/6" height="h-3" />
        <Skeleton width="w-4/6" height="h-3" />
        
        {/* Meta info */}
        <div className="flex items-center space-x-4 pt-1">
          <Skeleton width="w-16" height="h-3" />
          <Skeleton width="w-12" height="h-3" />
          <Skeleton width="w-20" height="h-3" />
        </div>
      </div>
    </div>
  );
};

// Stats card skeleton
export const StatsCardSkeleton = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <div className={`p-6 rounded-2xl border ${
      isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
    }`}>
      <div className="text-center space-y-4">
        {/* Icon */}
        <Skeleton width="w-14" height="h-14" className="mx-auto rounded-2xl" />
        
        {/* Counter */}
        <Skeleton width="w-20" height="h-8" className="mx-auto" />
        
        {/* Label */}
        <Skeleton width="w-24" height="h-4" className="mx-auto" />
      </div>
    </div>
  );
};

// Action card skeleton
export const ActionCardSkeleton = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <div className={`p-8 rounded-2xl border ${
      isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    }`}>
      <div className="space-y-6">
        {/* Icon */}
        <Skeleton width="w-16" height="h-16" className="rounded-xl" />
        
        {/* Title */}
        <Skeleton width="w-3/4" height="h-6" />
        
        {/* Description */}
        <div className="space-y-2">
          <Skeleton width="w-full" height="h-4" />
          <Skeleton width="w-5/6" height="h-4" />
          <Skeleton width="w-4/6" height="h-4" />
        </div>
        
        {/* Footer info */}
        <Skeleton width="w-32" height="h-4" />
      </div>
    </div>
  );
};

// News card skeleton
export const NewsCardSkeleton = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <div className={`p-4 rounded-xl border ${
      isDarkMode ? 'border-gray-700' : 'border-gray-200'
    }`}>
      <div className="space-y-3">
        {/* Title */}
        <Skeleton width="w-full" height="h-5" />
        <Skeleton width="w-4/5" height="h-4" />
        
        {/* Content */}
        <div className="space-y-2">
          <Skeleton width="w-full" height="h-3" />
          <Skeleton width="w-3/4" height="h-3" />
        </div>
        
        {/* Meta */}
        <div className="flex items-center justify-between">
          <Skeleton width="w-20" height="h-3" />
          <Skeleton width="w-16" height="h-3" />
        </div>
      </div>
    </div>
  );
};

// Grid skeleton for multiple items
export const GridSkeleton = ({ 
  count = 4, 
  SkeletonComponent = Skeleton, 
  className = "grid grid-cols-1 md:grid-cols-2 gap-6" 
}) => {
  return (
    <div className={className}>
      {Array.from({ length: count }, (_, index) => (
        <SkeletonComponent key={`skeleton-${index}`} />
      ))}
    </div>
  );
};

// Loading overlay for sections
export const LoadingOverlay = ({ children, isLoading, SkeletonComponent }) => {
  if (isLoading && SkeletonComponent) {
    return <SkeletonComponent />;
  }
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  return children;
};

// Pulse animation for better loading feedback
export const PulseLoader = ({ size = 'md', color = 'blue' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };
  
  const colorClasses = {
    blue: 'border-blue-600',
    purple: 'border-purple-600',
    green: 'border-green-600',
    orange: 'border-orange-600'
  };
  
  return (
    <div className="flex items-center justify-center py-8">
      <div className={`animate-spin rounded-full border-b-2 ${sizeClasses[size]} ${colorClasses[color]}`}></div>
    </div>
  );
};

export default {
  Skeleton,
  TrendingCardSkeleton,
  CommunityPostSkeleton,
  StatsCardSkeleton,
  ActionCardSkeleton,
  NewsCardSkeleton,
  GridSkeleton,
  LoadingOverlay,
  PulseLoader
};
