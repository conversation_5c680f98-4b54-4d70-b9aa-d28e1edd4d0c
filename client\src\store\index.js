/**
 * Enterprise State Management Store
 * Centralized state management with React Query optimization and error boundaries
 */

import { QueryClient } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client-core';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';

// Enhanced Query Client Configuration
export const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Stale time - data is considered fresh for 5 minutes
        staleTime: 5 * 60 * 1000,
        
        // Cache time - data stays in cache for 30 minutes
        cacheTime: 30 * 60 * 1000,
        
        // Retry configuration
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors (client errors)
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },
        
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        
        // Refetch configuration
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: true,
        
        // Background refetch interval (10 minutes)
        refetchInterval: 10 * 60 * 1000,
        
        // Only refetch in background if data is stale
        refetchIntervalInBackground: false,
        
        // Suspense support
        suspense: false,
        
        // Error handling
        useErrorBoundary: (error) => {
          // Use error boundary for 5xx errors
          return error?.response?.status >= 500;
        },
        
        // Network mode
        networkMode: 'online',
      },
      
      mutations: {
        // Retry mutations once
        retry: 1,
        
        // Use error boundary for critical mutations
        useErrorBoundary: (error) => {
          return error?.response?.status >= 500;
        },
        
        // Network mode for mutations
        networkMode: 'online',
      },
    },
    
    // Query cache configuration
    queryCache: {
      onError: (error, query) => {
        
        // Log to analytics service
        if (window.gtag) {
          window.gtag('event', 'query_error', {
            error_message: error.message,
            query_key: JSON.stringify(query.queryKey),
          });
        }
      },
      
      onSuccess: (data, query) => {
      },
    },
    
    // Mutation cache configuration
    mutationCache: {
      onError: (error, variables, context, mutation) => {
        
        // Log to analytics service
        if (window.gtag) {
          window.gtag('event', 'mutation_error', {
            error_message: error.message,
            mutation_key: mutation.options.mutationKey?.[0] || 'unknown',
          });
        }
      },
      
      onSuccess: (data, variables, context, mutation) => {
      },
    },
  });
};

// Create persister for offline support
export const createPersister = () => {
  return createSyncStoragePersister({
    storage: window.localStorage,
    key: 'factcheck-query-cache',
    serialize: JSON.stringify,
    deserialize: JSON.parse,
    
    // Only persist certain query types
    filter: (query) => {
      const queryKey = query.queryKey[0];
      const persistableQueries = [
        'user-profile',
        'user-settings',
        'trending-articles',
        'community-stats',
        'user-history',
      ];
      
      return persistableQueries.includes(queryKey);
    },
  });
};

// Query key factories for consistent key management
export const queryKeys = {
  // User-related queries
  user: {
    all: ['user'],
    profile: () => [...queryKeys.user.all, 'profile'],
    settings: () => [...queryKeys.user.all, 'settings'],
    history: (page = 1) => [...queryKeys.user.all, 'history', page],
    favorites: () => [...queryKeys.user.all, 'favorites'],
    achievements: () => [...queryKeys.user.all, 'achievements'],
  },
  
  // Community-related queries
  community: {
    all: ['community'],
    posts: (filters = {}) => [...queryKeys.community.all, 'posts', filters],
    post: (id) => [...queryKeys.community.all, 'post', id],
    comments: (postId) => [...queryKeys.community.all, 'comments', postId],
    stats: () => [...queryKeys.community.all, 'stats'],
    trending: () => [...queryKeys.community.all, 'trending'],
  },
  
  // Link checking queries
  linkCheck: {
    all: ['link-check'],
    result: (url) => [...queryKeys.linkCheck.all, 'result', url],
    history: (page = 1) => [...queryKeys.linkCheck.all, 'history', page],
    stats: () => [...queryKeys.linkCheck.all, 'stats'],
  },
  
  // News and articles
  news: {
    all: ['news'],
    trending: () => [...queryKeys.news.all, 'trending'],
    latest: (category) => [...queryKeys.news.all, 'latest', category],
    search: (query) => [...queryKeys.news.all, 'search', query],
  },
  
  // Admin queries
  admin: {
    all: ['admin'],
    users: (filters = {}) => [...queryKeys.admin.all, 'users', filters],
    reports: (filters = {}) => [...queryKeys.admin.all, 'reports', filters],
    analytics: (timeRange) => [...queryKeys.admin.all, 'analytics', timeRange],
  },
};

// Mutation key factories
export const mutationKeys = {
  // User mutations
  user: {
    login: ['user', 'login'],
    logout: ['user', 'logout'],
    register: ['user', 'register'],
    updateProfile: ['user', 'update-profile'],
    updateSettings: ['user', 'update-settings'],
    changePassword: ['user', 'change-password'],
  },
  
  // Community mutations
  community: {
    createPost: ['community', 'create-post'],
    updatePost: ['community', 'update-post'],
    deletePost: ['community', 'delete-post'],
    votePost: ['community', 'vote-post'],
    createComment: ['community', 'create-comment'],
    updateComment: ['community', 'update-comment'],
    deleteComment: ['community', 'delete-comment'],
  },
  
  // Link checking mutations
  linkCheck: {
    checkUrl: ['link-check', 'check-url'],
    saveResult: ['link-check', 'save-result'],
    deleteResult: ['link-check', 'delete-result'],
  },
};

// Query invalidation helpers
export const invalidateQueries = {
  // Invalidate all user-related queries
  user: (queryClient) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
  },
  
  // Invalidate community queries
  community: (queryClient) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.community.all });
  },
  
  // Invalidate specific post and its comments
  communityPost: (queryClient, postId) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.community.post(postId) });
    queryClient.invalidateQueries({ queryKey: queryKeys.community.comments(postId) });
    queryClient.invalidateQueries({ queryKey: queryKeys.community.posts() });
  },
  
  // Invalidate link check history
  linkCheckHistory: (queryClient) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.linkCheck.history() });
    queryClient.invalidateQueries({ queryKey: queryKeys.linkCheck.stats() });
  },
};

// Optimistic update helpers
export const optimisticUpdates = {
  // Optimistic vote update
  votePost: (queryClient, postId, voteType) => {
    const postQueryKey = queryKeys.community.post(postId);
    const postsQueryKey = queryKeys.community.posts();
    
    // Update individual post
    queryClient.setQueryData(postQueryKey, (oldData) => {
      if (!oldData) return oldData;
      
      const newVotes = { ...oldData.votes };
      if (voteType === 'up') {
        newVotes.up = (newVotes.up || 0) + 1;
      } else if (voteType === 'down') {
        newVotes.down = (newVotes.down || 0) + 1;
      }
      
      return { ...oldData, votes: newVotes };
    });
    
    // Update posts list
    queryClient.setQueryData(postsQueryKey, (oldData) => {
      if (!oldData?.posts) return oldData;
      
      const updatedPosts = oldData.posts.map(post => {
        if (post.id === postId) {
          const newVotes = { ...post.votes };
          if (voteType === 'up') {
            newVotes.up = (newVotes.up || 0) + 1;
          } else if (voteType === 'down') {
            newVotes.down = (newVotes.down || 0) + 1;
          }
          return { ...post, votes: newVotes };
        }
        return post;
      });
      
      return { ...oldData, posts: updatedPosts };
    });
  },
  
  // Optimistic comment creation
  createComment: (queryClient, postId, newComment) => {
    const commentsQueryKey = queryKeys.community.comments(postId);
    
    queryClient.setQueryData(commentsQueryKey, (oldData) => {
      if (!oldData) return [newComment];
      return [...oldData, newComment];
    });
  },
};

// Background sync utilities
export const backgroundSync = {
  // Sync user data in background
  syncUserData: async (queryClient) => {
    try {
      await queryClient.prefetchQuery({
        queryKey: queryKeys.user.profile(),
        staleTime: 0, // Force fresh fetch
      });
      
      await queryClient.prefetchQuery({
        queryKey: queryKeys.user.settings(),
        staleTime: 0,
      });
      
    } catch (error) {
    }
  },
  
  // Sync community data
  syncCommunityData: async (queryClient) => {
    try {
      await queryClient.prefetchQuery({
        queryKey: queryKeys.community.stats(),
        staleTime: 0,
      });
      
      await queryClient.prefetchQuery({
        queryKey: queryKeys.community.trending(),
        staleTime: 0,
      });
      
    } catch (error) {
    }
  },
};

// Error recovery utilities
export const errorRecovery = {
  // Retry failed queries
  retryFailedQueries: (queryClient) => {
    queryClient.getQueryCache().getAll().forEach(query => {
      if (query.state.status === 'error') {
        queryClient.invalidateQueries({ queryKey: query.queryKey });
      }
    });
  },
  
  // Clear error states
  clearErrors: (queryClient) => {
    queryClient.getQueryCache().getAll().forEach(query => {
      if (query.state.status === 'error') {
        queryClient.setQueryData(query.queryKey, undefined);
      }
    });
  },
  
  // Reset specific query
  resetQuery: (queryClient, queryKey) => {
    queryClient.resetQueries({ queryKey });
  },
};

// Performance monitoring
export const performanceMonitoring = {
  // Monitor query performance
  monitorQueries: (queryClient) => {
    const cache = queryClient.getQueryCache();
    
    const stats = {
      totalQueries: cache.getAll().length,
      successfulQueries: cache.getAll().filter(q => q.state.status === 'success').length,
      errorQueries: cache.getAll().filter(q => q.state.status === 'error').length,
      loadingQueries: cache.getAll().filter(q => q.state.status === 'loading').length,
      staleQueries: cache.getAll().filter(q => q.isStale()).length,
    };
    
    return stats;
  },
  
  // Get cache size
  getCacheSize: (queryClient) => {
    const cache = queryClient.getQueryCache();
    const serialized = JSON.stringify(cache.getAll().map(q => q.state.data));
    const sizeInBytes = new Blob([serialized]).size;
    const sizeInMB = (sizeInBytes / 1024 / 1024).toFixed(2);
    
    return { bytes: sizeInBytes, mb: parseFloat(sizeInMB) };
  },
};

// Export default store configuration
export default {
  createQueryClient,
  createPersister,
  queryKeys,
  mutationKeys,
  invalidateQueries,
  optimisticUpdates,
  backgroundSync,
  errorRecovery,
  performanceMonitoring,
};
