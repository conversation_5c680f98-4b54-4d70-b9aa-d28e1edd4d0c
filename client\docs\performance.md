# Performance Optimization Guide

This guide covers comprehensive performance optimization strategies for the FactCheck Platform frontend to achieve enterprise-level performance standards.

## Performance Targets

### Core Web Vitals
- **Largest Contentful Paint (LCP)**: < 2.5 seconds
- **First Input Delay (FID)**: < 100 milliseconds
- **Cumulative Layout Shift (CLS)**: < 0.1

### Additional Metrics
- **First Contentful Paint (FCP)**: < 1.8 seconds
- **Time to Interactive (TTI)**: < 3.8 seconds
- **Total Blocking Time (TBT)**: < 200 milliseconds
- **Speed Index**: < 3.4 seconds

### Bundle Size Targets
- **Initial Bundle**: < 200KB gzipped
- **Total Bundle**: < 500KB gzipped
- **Individual Chunks**: < 100KB gzipped

## Code Splitting and Lazy Loading

### Route-Based Code Splitting

```javascript
// App.js - Lazy load page components
import { lazy, Suspense } from 'react';
import { createLazyRoute } from './components/LazyComponents';

// Critical routes (loaded immediately)
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';

// Non-critical routes (lazy loaded)
const LazyDashboardPage = createLazyRoute(() => import('./pages/DashboardPage'));
const LazyCommunityPage = createLazyRoute(() => import('./pages/CommunityPage'));
const LazyCheckLinkPage = createLazyRoute(() => import('./pages/CheckLinkPage'));

function App() {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/login" element={<LoginPage />} />
      <Route path="/dashboard" element={<LazyDashboardPage />} />
      <Route path="/community" element={<LazyCommunityPage />} />
      <Route path="/check" element={<LazyCheckLinkPage />} />
    </Routes>
  );
}
```

### Component-Based Code Splitting

```javascript
// LazyComponents.js - Component-level splitting
import { lazy } from 'react';

// Heavy components loaded on demand
export const LazyChart = lazy(() => import('./ui/Chart'));
export const LazyDataTable = lazy(() => import('./ui/DataTable'));
export const LazyImageGallery = lazy(() => import('./ui/ImageGallery'));

// Conditional loading based on user permissions
export const LazyAdminPanel = lazy(() => 
  import('./admin/AdminPanel').then(module => ({
    default: module.AdminPanel
  }))
);
```

### Dynamic Imports with Error Handling

```javascript
// utils/dynamicImport.js
export const dynamicImport = async (importFunc, retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await importFunc();
    } catch (error) {
      console.warn(`Import attempt ${i + 1} failed:`, error);
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};

// Usage
const LazyComponent = lazy(() => 
  dynamicImport(() => import('./HeavyComponent'))
);
```

## Image Optimization

### Responsive Images with WebP Support

```javascript
// OptimizedImage.js
import { useState, useEffect } from 'react';

const OptimizedImage = ({ src, alt, sizes = '100vw', ...props }) => {
  const [webpSupported, setWebpSupported] = useState(false);

  useEffect(() => {
    // Check WebP support
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    setWebpSupported(
      canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
    );
  }, []);

  const generateSrcSet = (baseSrc, format) => {
    const breakpoints = [320, 640, 768, 1024, 1280, 1920];
    return breakpoints
      .map(bp => `${baseSrc}?w=${bp}&f=${format} ${bp}w`)
      .join(', ');
  };

  return (
    <picture>
      {webpSupported && (
        <source
          srcSet={generateSrcSet(src, 'webp')}
          sizes={sizes}
          type="image/webp"
        />
      )}
      <source
        srcSet={generateSrcSet(src, 'jpg')}
        sizes={sizes}
        type="image/jpeg"
      />
      <img
        src={src}
        alt={alt}
        loading="lazy"
        decoding="async"
        {...props}
      />
    </picture>
  );
};
```

### Image Lazy Loading with Intersection Observer

```javascript
// hooks/useImageLazyLoading.js
import { useEffect, useRef, useState } from 'react';

export const useImageLazyLoading = (options = {}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01,
        ...options
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return { imgRef, isInView, isLoaded, setIsLoaded };
};
```

## Bundle Optimization

### Webpack Bundle Analysis

```bash
# Analyze bundle composition
npm run analyze

# Check for duplicate dependencies
npx webpack-bundle-analyzer build/static/js/*.js
```

### Tree Shaking Optimization

```javascript
// utils/optimizedImports.js
// ❌ Bad - imports entire library
import _ from 'lodash';

// ✅ Good - imports only needed functions
import { debounce, throttle } from 'lodash';

// ❌ Bad - imports entire icon library
import * as Icons from 'lucide-react';

// ✅ Good - imports specific icons
import { Search, User, Settings } from 'lucide-react';
```

### Dynamic Polyfills

```javascript
// utils/polyfills.js
export const loadPolyfills = async () => {
  const polyfills = [];

  // Check for IntersectionObserver support
  if (!window.IntersectionObserver) {
    polyfills.push(import('intersection-observer'));
  }

  // Check for ResizeObserver support
  if (!window.ResizeObserver) {
    polyfills.push(import('@juggle/resize-observer'));
  }

  // Load polyfills in parallel
  await Promise.all(polyfills);
};
```

## Caching Strategies

### React Query Optimization

```javascript
// store/queryConfig.js
export const queryConfig = {
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes
      staleTime: 5 * 60 * 1000,
      
      // Keep data in cache for 30 minutes
      cacheTime: 30 * 60 * 1000,
      
      // Background refetch for stale data
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      
      // Retry configuration
      retry: (failureCount, error) => {
        if (error?.status >= 400 && error?.status < 500) {
          return false; // Don't retry client errors
        }
        return failureCount < 3;
      },
      
      // Exponential backoff
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
};
```

### Service Worker Caching

```javascript
// public/sw.js - Service Worker for caching
const CACHE_NAME = 'factcheck-v1';
const STATIC_ASSETS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

// Install event - cache static assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(STATIC_ASSETS))
  );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
  );
});
```

### Browser Caching Headers

```javascript
// vercel.json - Caching configuration
{
  "headers": [
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    },
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=0, must-revalidate"
        }
      ]
    }
  ]
}
```

## Runtime Performance

### React Performance Optimization

```javascript
// hooks/useOptimizedCallback.js
import { useCallback, useMemo, useRef } from 'react';

// Optimized callback that doesn't change on every render
export const useOptimizedCallback = (callback, deps) => {
  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  return useCallback((...args) => {
    return callbackRef.current(...args);
  }, deps);
};

// Memoized component with shallow comparison
export const MemoizedComponent = React.memo(Component, (prevProps, nextProps) => {
  // Custom comparison logic
  return Object.keys(prevProps).every(
    key => prevProps[key] === nextProps[key]
  );
});
```

### Virtual Scrolling for Large Lists

```javascript
// components/VirtualList.js
import { FixedSizeList as List } from 'react-window';

const VirtualList = ({ items, height = 400, itemHeight = 50 }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      {items[index]}
    </div>
  );

  return (
    <List
      height={height}
      itemCount={items.length}
      itemSize={itemHeight}
      overscanCount={5} // Render 5 extra items for smooth scrolling
    >
      {Row}
    </List>
  );
};
```

### Debounced Search

```javascript
// hooks/useDebouncedSearch.js
import { useState, useEffect, useMemo } from 'react';
import { debounce } from 'lodash';

export const useDebouncedSearch = (searchFunction, delay = 300) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery) => {
      if (!searchQuery.trim()) {
        setResults([]);
        return;
      }

      setIsLoading(true);
      try {
        const searchResults = await searchFunction(searchQuery);
        setResults(searchResults);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, delay),
    [searchFunction, delay]
  );

  useEffect(() => {
    debouncedSearch(query);
    return () => debouncedSearch.cancel();
  }, [query, debouncedSearch]);

  return { query, setQuery, results, isLoading };
};
```

## Animation Performance

### GSAP Performance Optimization

```javascript
// utils/gsapOptimization.js
import { gsap } from 'gsap';

// Optimized animation settings
export const optimizedGSAP = {
  // Use transform3d for hardware acceleration
  set: (target, vars) => {
    gsap.set(target, {
      force3D: true,
      ...vars
    });
  },

  // Batch DOM reads and writes
  batchUpdate: (animations) => {
    gsap.ticker.add(() => {
      animations.forEach(animation => animation());
    }, true); // Execute once
  },

  // Optimize for 60fps
  timeline: (options = {}) => {
    return gsap.timeline({
      ease: "power2.out",
      duration: 0.3,
      ...options
    });
  }
};

// Intersection Observer for scroll animations
export const createScrollAnimation = (selector, animation) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animation(entry.target);
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.1, rootMargin: '50px' }
  );

  document.querySelectorAll(selector).forEach(el => {
    observer.observe(el);
  });

  return observer;
};
```

## Monitoring and Measurement

### Performance Monitoring Setup

```javascript
// utils/performanceMonitoring.js
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.init();
  }

  init() {
    // Collect Core Web Vitals
    getCLS(this.handleMetric.bind(this));
    getFID(this.handleMetric.bind(this));
    getFCP(this.handleMetric.bind(this));
    getLCP(this.handleMetric.bind(this));
    getTTFB(this.handleMetric.bind(this));

    // Monitor custom metrics
    this.monitorBundleSize();
    this.monitorMemoryUsage();
    this.monitorNetworkRequests();
  }

  handleMetric(metric) {
    this.metrics[metric.name] = metric.value;
    
    // Send to analytics
    if (window.gtag) {
      window.gtag('event', metric.name, {
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        event_category: 'Web Vitals',
        non_interaction: true,
      });
    }

    // Log performance issues
    this.checkPerformanceThresholds(metric);
  }

  checkPerformanceThresholds(metric) {
    const thresholds = {
      LCP: 2500,
      FID: 100,
      CLS: 0.1,
      FCP: 1800,
      TTFB: 600
    };

    if (metric.value > thresholds[metric.name]) {
      console.warn(`Performance threshold exceeded for ${metric.name}:`, metric.value);
    }
  }

  monitorBundleSize() {
    // Monitor JavaScript bundle size
    const scripts = Array.from(document.scripts);
    let totalSize = 0;

    scripts.forEach(script => {
      if (script.src && script.src.includes('static/js/')) {
        fetch(script.src, { method: 'HEAD' })
          .then(response => {
            const size = response.headers.get('content-length');
            if (size) {
              totalSize += parseInt(size);
              console.log(`Bundle size: ${(totalSize / 1024).toFixed(2)} KB`);
            }
          });
      }
    });
  }

  monitorMemoryUsage() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        const memoryInfo = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        };

        if (memoryInfo.used > 50) { // Alert if using more than 50MB
          console.warn('High memory usage detected:', memoryInfo);
        }
      }, 30000); // Check every 30 seconds
    }
  }

  monitorNetworkRequests() {
    // Monitor slow network requests
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach(entry => {
        if (entry.duration > 1000) { // Requests taking more than 1 second
          console.warn('Slow network request:', entry.name, entry.duration);
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  getReport() {
    return {
      metrics: this.metrics,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };
  }
}

export const performanceMonitor = new PerformanceMonitor();
```

### Lighthouse CI Integration

```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI

on:
  pull_request:
    branches: [main]

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build app
        run: npm run build
      
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
```

## Performance Best Practices

### 1. Component Optimization
- Use `React.memo()` for expensive components
- Implement proper `shouldComponentUpdate` logic
- Avoid inline functions and objects in JSX
- Use `useCallback` and `useMemo` appropriately

### 2. Bundle Optimization
- Implement code splitting at route and component levels
- Use dynamic imports for heavy libraries
- Optimize third-party dependencies
- Remove unused code with tree shaking

### 3. Network Optimization
- Implement proper caching strategies
- Use CDN for static assets
- Optimize API requests with batching
- Implement request deduplication

### 4. Runtime Optimization
- Minimize DOM manipulations
- Use virtual scrolling for large lists
- Implement proper error boundaries
- Optimize re-renders with proper state management

### 5. Asset Optimization
- Compress and optimize images
- Use modern image formats (WebP, AVIF)
- Implement lazy loading for images
- Minimize CSS and JavaScript

---

For more detailed performance analysis, use the built-in performance monitoring tools and refer to the [monitoring guide](./monitoring.md).
