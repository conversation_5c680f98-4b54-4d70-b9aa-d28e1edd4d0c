/**
 * Optimized Image Component with lazy loading, WebP support, and performance monitoring
 * Enterprise-level image optimization for maximum performance
 */

import React, { useState, useRef, useEffect } from 'react';
import { ImageOptimization } from '../../utils/performanceOptimization';

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder = true,
  lazy = true,
  quality = 80,
  sizes = '100vw',
  priority = false,
  onLoad,
  onError,
  fallbackSrc = '/images/placeholder.jpg',
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(priority ? src : '');
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  // Generate responsive image sources
  const generateSources = (baseSrc) => {
    if (!baseSrc) return [];
    
    const breakpoints = [320, 640, 768, 1024, 1280, 1920];
    const webpSupported = ImageOptimization.supportsWebP();
    
    const sources = [];
    
    // WebP sources if supported
    if (webpSupported) {
      sources.push({
        type: 'image/webp',
        srcSet: breakpoints.map(bp => 
          `${baseSrc}?w=${bp}&q=${quality}&f=webp ${bp}w`
        ).join(', ')
      });
    }
    
    // Fallback sources
    sources.push({
      type: 'image/jpeg',
      srcSet: breakpoints.map(bp => 
        `${baseSrc}?w=${bp}&q=${quality}&f=jpg ${bp}w`
      ).join(', ')
    });
    
    return sources;
  };

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || currentSrc) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setCurrentSrc(src);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
      observerRef.current = observer;
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [src, lazy, priority, currentSrc]);

  // Handle image load
  const handleLoad = (event) => {
    setIsLoaded(true);
    setIsError(false);
    
    // Performance tracking
    const loadTime = performance.now() - (window.imageLoadStart || 0);
    
    if (onLoad) {
      onLoad(event);
    }
  };

  // Handle image error
  const handleError = (event) => {
    setIsError(true);
    setCurrentSrc(fallbackSrc);
    
    
    if (onError) {
      onError(event);
    }
  };

  // Generate placeholder styles
  const placeholderStyles = placeholder ? {
    backgroundColor: '#f3f4f6',
    backgroundImage: `url("data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='18' fill='%23d1d5db' text-anchor='middle' dy='.3em'%3eLoading...%3c/text%3e%3c/svg%3e")`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    minHeight: height || '200px',
  } : {};

  // Base image styles
  const imageStyles = {
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 1 : 0,
    ...placeholderStyles,
  };

  // Responsive image with picture element
  if (currentSrc && !isError) {
    const sources = generateSources(currentSrc);
    
    return (
      <picture className={`block ${className}`} ref={imgRef}>
        {sources.map((source, index) => (
          <source
            key={index}
            type={source.type}
            srcSet={source.srcSet}
            sizes={sizes}
          />
        ))}
        <img
          src={currentSrc}
          alt={alt}
          width={width}
          height={height}
          loading={lazy && !priority ? 'lazy' : 'eager'}
          decoding="async"
          style={imageStyles}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      </picture>
    );
  }

  // Placeholder or error state
  return (
    <div
      ref={imgRef}
      className={`${className} flex items-center justify-center`}
      style={{
        width: width || '100%',
        height: height || '200px',
        ...placeholderStyles,
      }}
    >
      {isError ? (
        <div className="text-center text-gray-500">
          <svg
            className="w-12 h-12 mx-auto mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <p className="text-sm">Failed to load image</p>
        </div>
      ) : (
        !currentSrc && (
          <div className="text-center text-gray-400">
            <div className="animate-pulse">
              <div className="w-12 h-12 bg-gray-300 rounded mx-auto mb-2"></div>
              <p className="text-sm">Loading...</p>
            </div>
          </div>
        )
      )}
    </div>
  );
};

// Higher-order component for image optimization
export const withImageOptimization = (WrappedComponent) => {
  return function OptimizedImageWrapper(props) {
    // Track image load start time
    useEffect(() => {
      window.imageLoadStart = performance.now();
    }, []);

    return <WrappedComponent {...props} />;
  };
};

// Image gallery component with lazy loading
export const OptimizedImageGallery = ({ images, columns = 3, gap = 4 }) => {
  const [visibleImages, setVisibleImages] = useState(6);
  
  const loadMore = () => {
    setVisibleImages(prev => Math.min(prev + 6, images.length));
  };

  return (
    <div className="space-y-4">
      <div 
        className={`grid grid-cols-1 md:grid-cols-${columns} gap-${gap}`}
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {images.slice(0, visibleImages).map((image, index) => (
          <OptimizedImage
            key={image.id || index}
            src={image.src}
            alt={image.alt || `Image ${index + 1}`}
            className="rounded-lg shadow-md hover:shadow-lg transition-shadow"
            lazy={index > 2} // Don't lazy load first 3 images
            priority={index < 3} // Prioritize first 3 images
          />
        ))}
      </div>
      
      {visibleImages < images.length && (
        <div className="text-center">
          <button
            onClick={loadMore}
            className="btn btn-primary"
          >
            Load More ({images.length - visibleImages} remaining)
          </button>
        </div>
      )}
    </div>
  );
};

// Avatar component with optimized loading
export const OptimizedAvatar = ({ 
  src, 
  alt, 
  size = 40, 
  fallback = null,
  className = '' 
}) => {
  const [hasError, setHasError] = useState(false);
  
  if (hasError || !src) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-300 rounded-full ${className}`}
        style={{ width: size, height: size }}
      >
        {fallback || (
          <span className="text-gray-600 font-medium">
            {alt?.charAt(0)?.toUpperCase() || '?'}
          </span>
        )}
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={`rounded-full object-cover ${className}`}
      onError={() => setHasError(true)}
      priority={size > 100} // Prioritize large avatars
    />
  );
};

export default OptimizedImage;
