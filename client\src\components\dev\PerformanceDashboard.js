/**
 * Performance Monitoring Dashboard for Development
 * Real-time performance metrics and optimization insights
 */

import React, { useState, useEffect } from 'react';
import { performanceMonitor, PerformanceBudget } from '../../utils/performanceOptimization';
import { gsapAnimations } from '../../utils/gsapAnimations';

const PerformanceDashboard = ({ isVisible = false, onClose }) => {
  const [metrics, setMetrics] = useState({});
  const [gsapMetrics, setGsapMetrics] = useState({});
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (!isVisible) return;

    const updateMetrics = () => {
      setMetrics(performanceMonitor.getMetrics());
      setGsapMetrics(gsapAnimations.getPerformanceMetrics());
    };

    // Update metrics every second
    const interval = setInterval(updateMetrics, 1000);
    updateMetrics(); // Initial update

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) return null;

  const getMetricColor = (value, threshold, reverse = false) => {
    const isGood = reverse ? value > threshold : value < threshold;
    return isGood ? 'text-green-600' : 'text-red-600';
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 max-w-md">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Performance Monitor
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg
              className={`w-4 h-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <button
            onClick={onClose}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Metrics */}
      <div className="p-4 space-y-4">
        {/* Core Web Vitals */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Core Web Vitals
          </h4>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <div className={`font-bold ${getMetricColor(metrics.lcp, 2500)}`}>
                {metrics.lcp ? `${Math.round(metrics.lcp)}ms` : '-'}
              </div>
              <div className="text-gray-500">LCP</div>
            </div>
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <div className={`font-bold ${getMetricColor(metrics.fid, 100)}`}>
                {metrics.fid ? `${Math.round(metrics.fid)}ms` : '-'}
              </div>
              <div className="text-gray-500">FID</div>
            </div>
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <div className={`font-bold ${getMetricColor(metrics.cls, 0.1)}`}>
                {metrics.cls ? metrics.cls.toFixed(3) : '-'}
              </div>
              <div className="text-gray-500">CLS</div>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Performance
          </h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <span>FPS:</span>
              <span className={`font-bold ${getMetricColor(metrics.fps, 30, true)}`}>
                {metrics.fps || 60}
              </span>
            </div>
            <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <span>Long Tasks:</span>
              <span className={`font-bold ${getMetricColor(metrics.longTasks, 0)}`}>
                {metrics.longTasks || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Memory Usage */}
        {metrics.memoryUsage && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Memory Usage
            </h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Used:</span>
                <span className="font-bold">{metrics.memoryUsage.used} MB</span>
              </div>
              <div className="flex justify-between">
                <span>Total:</span>
                <span>{metrics.memoryUsage.total} MB</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${(metrics.memoryUsage.used / metrics.memoryUsage.total) * 100}%`
                  }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* GSAP Metrics */}
        {isExpanded && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              GSAP Animations
            </h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>Timelines:</span>
                <span className="font-bold">{gsapMetrics.activeTimelines || 0}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>ScrollTriggers:</span>
                <span className="font-bold">{gsapMetrics.scrollTriggers || 0}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>Contexts:</span>
                <span className="font-bold">{gsapMetrics.activeContexts || 0}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span>Reduced Motion:</span>
                <span className={`font-bold ${gsapMetrics.isReducedMotion ? 'text-yellow-600' : 'text-green-600'}`}>
                  {gsapMetrics.isReducedMotion ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Performance Budget Status */}
        {isExpanded && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Budget Status
            </h4>
            <div className="space-y-1 text-xs">
              {PerformanceBudget.checkBudget(metrics).length === 0 ? (
                <div className="flex items-center text-green-600">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  All budgets met
                </div>
              ) : (
                <div className="space-y-1">
                  {PerformanceBudget.checkBudget(metrics).map((violation, index) => (
                    <div key={index} className="flex items-start text-red-600">
                      <svg className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span>{violation}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => {
              console.log('📊 Current Performance Metrics:', metrics);
              console.log('🎨 GSAP Metrics:', gsapMetrics);
            }}
            className="flex-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
          >
            Log Metrics
          </button>
          <button
            onClick={() => {
              performanceMonitor.cleanup();
              gsapAnimations.cleanup();
              console.log('🧹 Performance monitoring cleaned up');
            }}
            className="flex-1 px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
          >
            Cleanup
          </button>
        </div>
      </div>
    </div>
  );
};

// Performance monitoring toggle component
export const PerformanceToggle = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleKeyPress = (e) => {
      // Ctrl+Shift+P to toggle performance dashboard
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-40 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        title="Performance Monitor (Ctrl+Shift+P)"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      </button>

      {/* Dashboard */}
      <PerformanceDashboard
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
      />
    </>
  );
};

export default PerformanceDashboard;
