/**
 * Fact-Check Workflow E2E Tests
 * Complete user journey testing for the fact-checking platform
 */

describe('Fact-Check Platform Workflow', () => {
  beforeEach(() => {
    // Seed test data
    cy.seedTestData();
    
    // Mock external APIs
    cy.mockApiResponse('POST', '/api/check-link', {
      url: 'https://example.com/test-article',
      trustScore: 0.85,
      analysis: {
        domain: 'example.com',
        ssl: true,
        reputation: 'good',
        contentAnalysis: 'Content appears factual and well-sourced.',
      },
    });
  });

  afterEach(() => {
    cy.cleanTestData();
  });

  describe('User Authentication Flow', () => {
    it('allows user to register, login, and logout', () => {
      // Test registration
      cy.visit('/');
      cy.get('[data-testid="register-link"]').click();
      
      cy.register({
        email: '<EMAIL>',
        password: 'securepassword123',
        displayName: 'New Test User',
      });
      
      // Test login
      cy.visit('/login');
      cy.login('<EMAIL>', 'securepassword123');
      
      // Verify dashboard access
      cy.url().should('include', '/dashboard');
      cy.get('[data-testid="welcome-message"]').should('contain', 'New Test User');
      
      // Test logout
      cy.logout();
      cy.url().should('eq', Cypress.config().baseUrl + '/');
    });

    it('handles login errors gracefully', () => {
      cy.visit('/login');
      
      cy.fillForm({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });
      
      cy.submitForm('login-button');
      cy.expectError('Invalid credentials');
    });
  });

  describe('Link Checking Workflow', () => {
    beforeEach(() => {
      cy.login();
    });

    it('allows user to check a link and view results', () => {
      cy.navigateTo('checkLink');
      
      // Enter URL to check
      cy.get('[data-testid="url-input"]').type('https://example.com/test-article');
      cy.get('[data-testid="check-button"]').click();
      
      // Wait for analysis
      cy.expectLoading();
      cy.waitForApi();
      cy.waitForLoading();
      
      // Verify results
      cy.get('[data-testid="trust-score"]').should('contain', '85%');
      cy.get('[data-testid="analysis-result"]').should('contain', 'Content appears factual');
      cy.get('[data-testid="domain-info"]').should('contain', 'example.com');
      
      // Test saving result
      cy.get('[data-testid="save-result-button"]').click();
      cy.expectSuccess('Result saved successfully');
    });

    it('handles invalid URLs appropriately', () => {
      cy.navigateTo('checkLink');
      
      cy.get('[data-testid="url-input"]').type('not-a-valid-url');
      cy.get('[data-testid="check-button"]').click();
      
      cy.expectError('Please enter a valid URL');
    });

    it('displays loading state during analysis', () => {
      cy.navigateTo('checkLink');
      
      // Mock slow API response
      cy.mockApiResponse('POST', '/api/check-link', {}, 200, { delay: 2000 });
      
      cy.get('[data-testid="url-input"]').type('https://example.com/slow-check');
      cy.get('[data-testid="check-button"]').click();
      
      cy.expectLoading();
      cy.get('[data-testid="check-button"]').should('be.disabled');
      
      cy.waitForLoading();
      cy.get('[data-testid="check-button"]').should('not.be.disabled');
    });
  });

  describe('Community Features', () => {
    beforeEach(() => {
      cy.login();
    });

    it('allows user to view and interact with community posts', () => {
      cy.navigateTo('community');
      
      // Verify posts are displayed
      cy.get('[data-testid="community-post"]').should('have.length.at.least', 1);
      
      // Test voting
      cy.get('[data-testid="upvote-button"]').first().click();
      cy.get('[data-testid="vote-count"]').first().should('contain', '1');
      
      // Test commenting
      cy.get('[data-testid="comment-button"]').first().click();
      cy.get('[data-testid="comment-input"]').type('This is a test comment');
      cy.get('[data-testid="submit-comment-button"]').click();
      
      cy.expectSuccess('Comment added successfully');
      cy.get('[data-testid="comment"]').should('contain', 'This is a test comment');
    });

    it('allows user to submit new posts', () => {
      cy.navigateTo('community');
      
      cy.get('[data-testid="new-post-button"]').click();
      
      cy.fillForm({
        title: 'Test Community Post',
        content: 'This is a test post for the community.',
        url: 'https://example.com/test-source',
      });
      
      cy.submitForm('submit-post-button');
      
      cy.expectSuccess('Post submitted successfully');
      cy.get('[data-testid="community-post"]').first().should('contain', 'Test Community Post');
    });
  });

  describe('Dashboard Features', () => {
    beforeEach(() => {
      cy.login();
    });

    it('displays user statistics and recent activity', () => {
      cy.navigateTo('dashboard');
      
      // Verify dashboard components
      cy.get('[data-testid="user-stats"]').should('be.visible');
      cy.get('[data-testid="recent-checks"]').should('be.visible');
      cy.get('[data-testid="trending-articles"]').should('be.visible');
      
      // Test quick actions
      cy.get('[data-testid="quick-check-button"]').click();
      cy.url().should('include', '/check');
    });

    it('shows personalized content based on user activity', () => {
      cy.navigateTo('dashboard');
      
      // Verify personalized sections
      cy.get('[data-testid="recommended-articles"]').should('be.visible');
      cy.get('[data-testid="user-achievements"]').should('be.visible');
    });
  });

  describe('Responsive Design', () => {
    beforeEach(() => {
      cy.login();
    });

    it('works correctly on mobile devices', () => {
      cy.setMobileViewport();
      
      cy.navigateTo('dashboard');
      
      // Test mobile navigation
      cy.get('[data-testid="mobile-menu-button"]').click();
      cy.get('[data-testid="mobile-menu"]').should('be.visible');
      
      // Test mobile-specific layouts
      cy.get('[data-testid="dashboard-grid"]').should('have.class', 'grid-cols-1');
    });

    it('adapts to tablet viewport', () => {
      cy.setTabletViewport();
      
      cy.navigateTo('community');
      
      // Verify tablet layout
      cy.get('[data-testid="community-grid"]').should('have.class', 'md:grid-cols-2');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.login();
    });

    it('meets WCAG accessibility standards', () => {
      cy.navigateTo('dashboard');
      cy.checkA11y();
      
      cy.navigateTo('community');
      cy.checkA11y();
      
      cy.navigateTo('checkLink');
      cy.checkA11y();
    });

    it('supports keyboard navigation', () => {
      cy.navigateTo('dashboard');
      
      // Test keyboard navigation through interactive elements
      cy.testKeyboardNavigation('[data-testid="interactive-element"]');
      
      // Test skip links
      cy.get('body').tab();
      cy.focused().should('contain', 'Skip to main content');
    });

    it('provides proper screen reader support', () => {
      cy.navigateTo('checkLink');
      
      cy.testScreenReader('[data-testid="url-input"]', 'Enter URL to check');
      cy.testScreenReader('[data-testid="check-button"]', 'Check URL');
    });
  });

  describe('Performance', () => {
    it('loads pages within acceptable time limits', () => {
      cy.visit('/');
      cy.measurePerformance('homepage');
      
      cy.login();
      cy.measurePerformance('dashboard');
      
      cy.navigateTo('community');
      cy.measurePerformance('community');
    });

    it('handles large datasets efficiently', () => {
      // Seed large dataset
      cy.task('seedLargeDataset', { posts: 100, comments: 500 });
      
      cy.login();
      cy.navigateTo('community');
      
      // Verify pagination works
      cy.get('[data-testid="pagination"]').should('be.visible');
      cy.get('[data-testid="next-page-button"]').click();
      
      // Verify performance is maintained
      cy.measurePerformance('community-large-dataset');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      cy.login();
    });

    it('handles network errors gracefully', () => {
      // Mock network error
      cy.mockApiResponse('GET', '/api/posts', {}, 500);
      
      cy.navigateTo('community');
      
      cy.expectError('Unable to load posts. Please try again.');
      cy.get('[data-testid="retry-button"]').should('be.visible');
    });

    it('recovers from temporary failures', () => {
      // Mock initial failure then success
      cy.mockApiResponse('GET', '/api/posts', {}, 500);
      
      cy.navigateTo('community');
      cy.expectError('Unable to load posts');
      
      // Mock successful retry
      cy.mockApiResponse('GET', '/api/posts', { posts: [] });
      cy.get('[data-testid="retry-button"]').click();
      
      cy.get('[data-testid="community-posts"]').should('be.visible');
    });
  });
});
