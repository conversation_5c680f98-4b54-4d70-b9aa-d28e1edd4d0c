/**
 * MSW Request Handlers
 * Mock API responses for testing and development
 */

import { rest } from 'msw';

// Mock data
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    displayName: '<PERSON>',
    emailVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    email: '<EMAIL>',
    displayName: '<PERSON>',
    emailVerified: true,
    createdAt: '2024-01-02T00:00:00Z',
  },
];

const mockPosts = [
  {
    id: '1',
    title: 'Test Article 1',
    content: 'This is a test article for fact-checking.',
    url: 'https://example.com/article1',
    authorId: '1',
    votes: { up: 15, down: 2 },
    status: 'verified',
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
  },
  {
    id: '2',
    title: 'Test Article 2',
    content: 'Another test article for the community.',
    url: 'https://example.com/article2',
    authorId: '2',
    votes: { up: 8, down: 1 },
    status: 'pending',
    createdAt: '2024-01-02T14:30:00Z',
    updatedAt: '2024-01-02T14:30:00Z',
  },
];

const mockComments = [
  {
    id: '1',
    postId: '1',
    authorId: '2',
    content: 'Great fact-check! Very thorough analysis.',
    votes: { up: 5, down: 0 },
    createdAt: '2024-01-01T11:00:00Z',
  },
  {
    id: '2',
    postId: '1',
    authorId: '1',
    content: 'Thanks for the feedback!',
    votes: { up: 2, down: 0 },
    createdAt: '2024-01-01T11:30:00Z',
  },
];

const mockTrendingArticles = [
  {
    id: '1',
    title: 'Breaking: Major Scientific Discovery',
    url: 'https://example.com/science-discovery',
    source: 'Science Daily',
    publishedAt: '2024-01-01T08:00:00Z',
    engagement: 1250,
    trustScore: 0.92,
  },
  {
    id: '2',
    title: 'Economic Policy Changes Announced',
    url: 'https://example.com/economic-policy',
    source: 'Financial Times',
    publishedAt: '2024-01-01T09:15:00Z',
    engagement: 890,
    trustScore: 0.88,
  },
];

export const handlers = [
  // Authentication endpoints
  rest.post('/api/auth/login', (req, res, ctx) => {
    const { email, password } = req.body;
    
    if (email === '<EMAIL>' && password === 'password123') {
      return res(
        ctx.status(200),
        ctx.json({
          user: mockUsers[0],
          token: 'mock-jwt-token',
        })
      );
    }
    
    return res(
      ctx.status(401),
      ctx.json({ error: 'Invalid credentials' })
    );
  }),

  rest.post('/api/auth/register', (req, res, ctx) => {
    const { email, password, displayName } = req.body;
    
    const newUser = {
      id: String(mockUsers.length + 1),
      email,
      displayName,
      emailVerified: false,
      createdAt: new Date().toISOString(),
    };
    
    mockUsers.push(newUser);
    
    return res(
      ctx.status(201),
      ctx.json({
        user: newUser,
        token: 'mock-jwt-token',
      })
    );
  }),

  rest.post('/api/auth/logout', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ message: 'Logged out successfully' }));
  }),

  rest.post('/api/auth/forgot-password', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ message: 'Password reset email sent' })
    );
  }),

  // User endpoints
  rest.get('/api/users/profile', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockUsers[0]));
  }),

  rest.put('/api/users/profile', (req, res, ctx) => {
    const updates = req.body;
    const updatedUser = { ...mockUsers[0], ...updates };
    mockUsers[0] = updatedUser;
    
    return res(ctx.status(200), ctx.json(updatedUser));
  }),

  // Posts/Articles endpoints
  rest.get('/api/posts', (req, res, ctx) => {
    const page = parseInt(req.url.searchParams.get('page') || '1');
    const limit = parseInt(req.url.searchParams.get('limit') || '10');
    const status = req.url.searchParams.get('status');
    
    let filteredPosts = mockPosts;
    if (status) {
      filteredPosts = mockPosts.filter(post => post.status === status);
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json({
        posts: paginatedPosts,
        pagination: {
          page,
          limit,
          total: filteredPosts.length,
          totalPages: Math.ceil(filteredPosts.length / limit),
        },
      })
    );
  }),

  rest.get('/api/posts/:id', (req, res, ctx) => {
    const { id } = req.params;
    const post = mockPosts.find(p => p.id === id);
    
    if (!post) {
      return res(ctx.status(404), ctx.json({ error: 'Post not found' }));
    }
    
    return res(ctx.status(200), ctx.json(post));
  }),

  rest.post('/api/posts', (req, res, ctx) => {
    const { title, content, url } = req.body;
    
    const newPost = {
      id: String(mockPosts.length + 1),
      title,
      content,
      url,
      authorId: '1', // Mock current user
      votes: { up: 0, down: 0 },
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockPosts.push(newPost);
    
    return res(ctx.status(201), ctx.json(newPost));
  }),

  rest.put('/api/posts/:id', (req, res, ctx) => {
    const { id } = req.params;
    const updates = req.body;
    
    const postIndex = mockPosts.findIndex(p => p.id === id);
    if (postIndex === -1) {
      return res(ctx.status(404), ctx.json({ error: 'Post not found' }));
    }
    
    mockPosts[postIndex] = {
      ...mockPosts[postIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    return res(ctx.status(200), ctx.json(mockPosts[postIndex]));
  }),

  rest.delete('/api/posts/:id', (req, res, ctx) => {
    const { id } = req.params;
    const postIndex = mockPosts.findIndex(p => p.id === id);
    
    if (postIndex === -1) {
      return res(ctx.status(404), ctx.json({ error: 'Post not found' }));
    }
    
    mockPosts.splice(postIndex, 1);
    return res(ctx.status(204));
  }),

  // Comments endpoints
  rest.get('/api/posts/:postId/comments', (req, res, ctx) => {
    const { postId } = req.params;
    const postComments = mockComments.filter(c => c.postId === postId);
    
    return res(ctx.status(200), ctx.json(postComments));
  }),

  rest.post('/api/posts/:postId/comments', (req, res, ctx) => {
    const { postId } = req.params;
    const { content } = req.body;
    
    const newComment = {
      id: String(mockComments.length + 1),
      postId,
      authorId: '1', // Mock current user
      content,
      votes: { up: 0, down: 0 },
      createdAt: new Date().toISOString(),
    };
    
    mockComments.push(newComment);
    
    return res(ctx.status(201), ctx.json(newComment));
  }),

  // Voting endpoints
  rest.post('/api/posts/:id/vote', (req, res, ctx) => {
    const { id } = req.params;
    const { type } = req.body; // 'up' or 'down'
    
    const post = mockPosts.find(p => p.id === id);
    if (!post) {
      return res(ctx.status(404), ctx.json({ error: 'Post not found' }));
    }
    
    if (type === 'up') {
      post.votes.up += 1;
    } else if (type === 'down') {
      post.votes.down += 1;
    }
    
    return res(ctx.status(200), ctx.json(post.votes));
  }),

  // Trending articles endpoint
  rest.get('/api/trending', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockTrendingArticles));
  }),

  // Link checking endpoint
  rest.post('/api/check-link', (req, res, ctx) => {
    const { url } = req.body;
    
    // Simulate link analysis
    const mockResult = {
      url,
      status: 'analyzed',
      trustScore: Math.random() * 0.4 + 0.6, // Random score between 0.6-1.0
      analysis: {
        domain: new URL(url).hostname,
        ssl: true,
        reputation: 'good',
        contentAnalysis: 'The content appears to be factual and well-sourced.',
        sources: ['Wikipedia', 'Reuters', 'BBC'],
      },
      timestamp: new Date().toISOString(),
    };
    
    return res(ctx.status(200), ctx.json(mockResult));
  }),

  // Search endpoint
  rest.get('/api/search', (req, res, ctx) => {
    const query = req.url.searchParams.get('q');
    const type = req.url.searchParams.get('type') || 'all';
    
    let results = [];
    
    if (type === 'posts' || type === 'all') {
      const matchingPosts = mockPosts.filter(post =>
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        post.content.toLowerCase().includes(query.toLowerCase())
      );
      results.push(...matchingPosts.map(post => ({ ...post, type: 'post' })));
    }
    
    if (type === 'articles' || type === 'all') {
      const matchingArticles = mockTrendingArticles.filter(article =>
        article.title.toLowerCase().includes(query.toLowerCase())
      );
      results.push(...matchingArticles.map(article => ({ ...article, type: 'article' })));
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        query,
        results,
        total: results.length,
      })
    );
  }),

  // Error simulation for testing
  rest.get('/api/error', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  // Slow response simulation for testing
  rest.get('/api/slow', (req, res, ctx) => {
    return res(
      ctx.delay(3000), // 3 second delay
      ctx.status(200),
      ctx.json({ message: 'This was a slow response' })
    );
  }),
];

export { mockUsers, mockPosts, mockComments, mockTrendingArticles };
