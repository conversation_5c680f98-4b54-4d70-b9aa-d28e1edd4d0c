# Enterprise UI Components Documentation

This document provides comprehensive documentation for the enterprise-grade UI components in the FactCheck platform.

## Table of Contents

1. [EnterpriseButton](#enterprisebutton)
2. [EnterpriseInput](#enterpriseinput)
3. [EnterpriseCard](#enterprisecard)
4. [Design Principles](#design-principles)
5. [Accessibility Guidelines](#accessibility-guidelines)
6. [Performance Considerations](#performance-considerations)

## EnterpriseButton

A comprehensive button component with animations, accessibility, and extensive customization options.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `string` | `'primary'` | Button style variant |
| `size` | `string` | `'md'` | Button size |
| `shape` | `string` | `'rounded'` | Button shape |
| `disabled` | `boolean` | `false` | Whether button is disabled |
| `loading` | `boolean` | `false` | Whether button is in loading state |
| `fullWidth` | `boolean` | `false` | Whether button takes full width |
| `leftIcon` | `ReactNode` | `null` | Icon to display on the left |
| `rightIcon` | `ReactNode` | `null` | Icon to display on the right |
| `animate` | `boolean` | `true` | Whether to enable animations |
| `animationType` | `string` | `'hover'` | Type of animation |

### Variants

- `primary` - Main brand button
- `secondary` - Secondary action button
- `accent` - Accent color button
- `success` - Success state button
- `warning` - Warning state button
- `error` - Error state button
- `ghost` - Transparent button
- `outline` - Outlined button

### Sizes

- `xs` - Extra small (px-2 py-1 text-xs)
- `sm` - Small (px-3 py-1.5 text-sm)
- `md` - Medium (px-4 py-2 text-base)
- `lg` - Large (px-6 py-3 text-lg)
- `xl` - Extra large (px-8 py-4 text-xl)

### Usage Examples

```jsx
// Basic button
<EnterpriseButton>Click me</EnterpriseButton>

// Primary button with icon
<EnterpriseButton variant="primary" leftIcon={<SaveIcon />}>
  Save Changes
</EnterpriseButton>

// Loading button
<EnterpriseButton loading loadingText="Saving...">
  Save
</EnterpriseButton>

// Animated button with glow effect
<EnterpriseButton animationType="glow" variant="accent">
  Special Action
</EnterpriseButton>
```

## EnterpriseInput

A comprehensive input component with validation, accessibility, and animations.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `string` | `'default'` | Input style variant |
| `size` | `string` | `'md'` | Input size |
| `label` | `string` | `undefined` | Input label |
| `placeholder` | `string` | `undefined` | Input placeholder |
| `helperText` | `string` | `undefined` | Helper text below input |
| `errorMessage` | `string` | `undefined` | Error message |
| `required` | `boolean` | `false` | Whether input is required |
| `disabled` | `boolean` | `false` | Whether input is disabled |
| `leftIcon` | `ReactNode` | `null` | Icon to display on the left |
| `rightIcon` | `ReactNode` | `null` | Icon to display on the right |
| `animate` | `boolean` | `true` | Whether to enable animations |
| `onValidate` | `function` | `undefined` | Custom validation function |
| `validation` | `object` | `{}` | Validation rules |

### Variants

- `default` - Standard input style
- `success` - Success state styling
- `warning` - Warning state styling
- `error` - Error state styling
- `ghost` - Minimal styling

### Usage Examples

```jsx
// Basic input
<EnterpriseInput
  label="Email Address"
  type="email"
  placeholder="Enter your email"
  required
/>

// Input with validation
<EnterpriseInput
  label="Password"
  type="password"
  validation={{
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    patternMessage: "Password must contain uppercase, lowercase, and number"
  }}
/>

// Search input
<SearchInput
  placeholder="Search articles..."
  onSearch={(query) => console.log('Searching:', query)}
  clearable
/>
```

## EnterpriseCard

A comprehensive card component with animations, interactions, and accessibility.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `string` | `'default'` | Card style variant |
| `size` | `string` | `'md'` | Card padding size |
| `radius` | `string` | `'lg'` | Card border radius |
| `hoverable` | `boolean` | `false` | Whether card has hover effects |
| `clickable` | `boolean` | `false` | Whether card is clickable |
| `selectable` | `boolean` | `false` | Whether card can be selected |
| `selected` | `boolean` | `false` | Whether card is currently selected |
| `loading` | `boolean` | `false` | Whether card is in loading state |
| `animate` | `boolean` | `true` | Whether to enable animations |
| `animationType` | `string` | `'hover'` | Type of animation |
| `header` | `ReactNode` | `undefined` | Card header content |
| `footer` | `ReactNode` | `undefined` | Card footer content |
| `actions` | `ReactNode` | `undefined` | Card action buttons |

### Variants

- `default` - Standard card with border
- `elevated` - Card with shadow, no border
- `outlined` - Card with thick border
- `filled` - Card with background fill
- `glass` - Glass morphism effect
- `gradient` - Gradient background

### Usage Examples

```jsx
// Basic card
<EnterpriseCard>
  <h3>Card Title</h3>
  <p>Card content goes here.</p>
</EnterpriseCard>

// Interactive card with header and actions
<EnterpriseCard
  hoverable
  clickable
  animationType="lift"
  header={
    <CardHeader
      title="Article Title"
      subtitle="Published 2 hours ago"
      actions={<MoreOptionsButton />}
    />
  }
  actions={
    <>
      <EnterpriseButton variant="ghost" size="sm">Cancel</EnterpriseButton>
      <EnterpriseButton variant="primary" size="sm">Read More</EnterpriseButton>
    </>
  }
>
  <p>Article preview content...</p>
</EnterpriseCard>

// Selectable card grid
<CardGrid columns={{ sm: 1, md: 2, lg: 3 }}>
  {articles.map(article => (
    <EnterpriseCard
      key={article.id}
      selectable
      selected={selectedArticles.includes(article.id)}
      onSelect={(selected) => handleSelect(article.id, selected)}
    >
      {article.content}
    </EnterpriseCard>
  ))}
</CardGrid>
```

## Design Principles

### 1. Consistency
- All components follow the same design tokens and patterns
- Consistent spacing, typography, and color usage
- Unified animation timing and easing functions

### 2. Accessibility
- WCAG AA compliance
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility

### 3. Performance
- Optimized animations using GSAP
- Lazy loading for complex components
- Minimal re-renders through proper memoization

### 4. Flexibility
- Extensive customization options
- Composable component architecture
- Support for custom styling and themes

## Accessibility Guidelines

### Keyboard Navigation
- All interactive components support keyboard navigation
- Tab order is logical and predictable
- Enter and Space keys trigger actions where appropriate

### Screen Readers
- Proper semantic HTML elements
- ARIA labels for complex interactions
- Status announcements for dynamic content

### Visual Accessibility
- High contrast color combinations
- Minimum 14px font size
- Clear focus indicators
- Support for reduced motion preferences

## Performance Considerations

### Animation Performance
- Hardware-accelerated animations using GSAP
- Respect for `prefers-reduced-motion`
- Efficient cleanup of animation timelines

### Bundle Size
- Tree-shakeable component exports
- Lazy loading for heavy components
- Optimized dependencies

### Runtime Performance
- Minimal re-renders through React.memo
- Efficient event handling
- Proper cleanup in useEffect hooks

## Best Practices

1. **Always provide meaningful labels** for form inputs and interactive elements
2. **Use semantic HTML** elements when possible
3. **Test with keyboard navigation** and screen readers
4. **Respect user preferences** for motion and contrast
5. **Provide loading states** for async operations
6. **Handle error states** gracefully with clear messaging
7. **Use consistent spacing** from the design token system
8. **Optimize for mobile** with responsive design patterns
