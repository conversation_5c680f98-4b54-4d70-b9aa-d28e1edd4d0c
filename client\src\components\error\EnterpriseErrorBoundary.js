/**
 * Enterprise Error Boundary System
 * Comprehensive error handling with recovery, logging, and user feedback
 */

import React, { Component } from 'react';
import { QueryErrorResetBoundary } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import EnterpriseButton from '../ui/EnterpriseButton';

// Error types for categorization
const ERROR_TYPES = {
  NETWORK: 'network',
  AUTHENTICATION: 'authentication',
  PERMISSION: 'permission',
  VALIDATION: 'validation',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown',
};

// Error severity levels
const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

class EnterpriseErrorBoundary extends Component {
  constructor(props) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      isRecovering: false,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({ errorInfo });
    
    // Categorize error
    const errorType = this.categorizeError(error);
    const severity = this.determineSeverity(error, errorType);
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught Error');
      console.groupEnd();
    }
    
    // Log to external service
    this.logError(error, errorInfo, errorType, severity);
    
    // Show user notification based on severity
    this.notifyUser(error, errorType, severity);
    
    // Attempt automatic recovery for certain error types
    this.attemptAutoRecovery(error, errorType);
  }

  categorizeError = (error) => {
    const message = error.message?.toLowerCase() || '';
    const stack = error.stack?.toLowerCase() || '';
    
    if (message.includes('network') || message.includes('fetch')) {
      return ERROR_TYPES.NETWORK;
    }
    
    if (message.includes('unauthorized') || message.includes('401')) {
      return ERROR_TYPES.AUTHENTICATION;
    }
    
    if (message.includes('forbidden') || message.includes('403')) {
      return ERROR_TYPES.PERMISSION;
    }
    
    if (message.includes('validation') || message.includes('400')) {
      return ERROR_TYPES.VALIDATION;
    }
    
    if (message.includes('500') || message.includes('server')) {
      return ERROR_TYPES.SERVER;
    }
    
    if (stack.includes('chunk') || message.includes('loading')) {
      return ERROR_TYPES.CLIENT;
    }
    
    return ERROR_TYPES.UNKNOWN;
  };

  determineSeverity = (error, errorType) => {
    switch (errorType) {
      case ERROR_TYPES.AUTHENTICATION:
      case ERROR_TYPES.PERMISSION:
        return ERROR_SEVERITY.HIGH;
      
      case ERROR_TYPES.SERVER:
        return ERROR_SEVERITY.CRITICAL;
      
      case ERROR_TYPES.NETWORK:
      case ERROR_TYPES.CLIENT:
        return ERROR_SEVERITY.MEDIUM;
      
      case ERROR_TYPES.VALIDATION:
        return ERROR_SEVERITY.LOW;
      
      default:
        return ERROR_SEVERITY.MEDIUM;
    }
  };

  logError = (error, errorInfo, errorType, severity) => {
    const errorData = {
      id: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      type: errorType,
      severity,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: JSON.parse(localStorage.getItem('user') || '{}').id,
      retryCount: this.state.retryCount,
    };
    
    // Log to external service (e.g., Sentry, LogRocket, etc.)
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: severity === ERROR_SEVERITY.CRITICAL,
        error_id: errorData.id,
        error_type: errorType,
      });
    }
    
    // Store in local storage for debugging
    const errorLog = JSON.parse(localStorage.getItem('errorLog') || '[]');
    errorLog.push(errorData);
    
    // Keep only last 10 errors
    if (errorLog.length > 10) {
      errorLog.shift();
    }
    
    localStorage.setItem('errorLog', JSON.stringify(errorLog));
  };

  notifyUser = (error, errorType, severity) => {
    const messages = {
      [ERROR_TYPES.NETWORK]: 'Network connection issue. Please check your internet connection.',
      [ERROR_TYPES.AUTHENTICATION]: 'Authentication error. Please log in again.',
      [ERROR_TYPES.PERMISSION]: 'You don\'t have permission to perform this action.',
      [ERROR_TYPES.VALIDATION]: 'Invalid data provided. Please check your input.',
      [ERROR_TYPES.SERVER]: 'Server error occurred. Our team has been notified.',
      [ERROR_TYPES.CLIENT]: 'Application error occurred. Trying to recover...',
      [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred.',
    };
    
    const message = messages[errorType] || messages[ERROR_TYPES.UNKNOWN];
    
    if (severity === ERROR_SEVERITY.CRITICAL) {
      toast.error(message, { duration: 8000 });
    } else if (severity === ERROR_SEVERITY.HIGH) {
      toast.error(message, { duration: 6000 });
    } else if (severity === ERROR_SEVERITY.MEDIUM) {
      toast.error(message, { duration: 4000 });
    }
    // Don't show toast for low severity errors
  };

  attemptAutoRecovery = (error, errorType) => {
    // Auto-recovery strategies
    switch (errorType) {
      case ERROR_TYPES.NETWORK:
        // Retry network requests after a delay
        setTimeout(() => {
          if (this.state.retryCount < 3) {
            this.handleRetry();
          }
        }, 2000);
        break;
      
      case ERROR_TYPES.AUTHENTICATION:
        // Redirect to login
        setTimeout(() => {
          window.location.href = '/login';
        }, 3000);
        break;
      
      case ERROR_TYPES.CLIENT:
        // Reload the page for client-side errors
        setTimeout(() => {
          window.location.reload();
        }, 5000);
        break;
      
      default:
        // No auto-recovery for other error types
        break;
    }
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
      isRecovering: true,
    }));
    
    // Reset recovering state after a delay
    setTimeout(() => {
      this.setState({ isRecovering: false });
    }, 1000);
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportError = () => {
    const errorData = {
      id: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    };
    
    // Open email client with error details
    const subject = encodeURIComponent('Error Report - FactCheck Platform');
    const body = encodeURIComponent(`
Error ID: ${errorData.id}
Message: ${errorData.message}
URL: ${errorData.url}
Timestamp: ${errorData.timestamp}

Please describe what you were doing when this error occurred:


Technical Details:
${errorData.stack}
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      const { error } = this.state;
      const errorType = this.categorizeError(error);
      const severity = this.determineSeverity(error, errorType);
      
      // Custom fallback UI based on error type
      if (this.props.fallback) {
        return this.props.fallback(error, this.handleRetry);
      }
      
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center">
            {/* Error Icon */}
            <div className="mb-6">
              {severity === ERROR_SEVERITY.CRITICAL ? (
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              ) : (
                <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-8 h-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              )}
            </div>
            
            {/* Error Title */}
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {severity === ERROR_SEVERITY.CRITICAL ? 'Critical Error' : 'Something went wrong'}
            </h1>
            
            {/* Error Message */}
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {errorType === ERROR_TYPES.NETWORK && 'Please check your internet connection and try again.'}
              {errorType === ERROR_TYPES.AUTHENTICATION && 'Your session has expired. Please log in again.'}
              {errorType === ERROR_TYPES.PERMISSION && 'You don\'t have permission to access this resource.'}
              {errorType === ERROR_TYPES.SERVER && 'Our servers are experiencing issues. We\'re working to fix this.'}
              {errorType === ERROR_TYPES.CLIENT && 'The application encountered an error. Refreshing may help.'}
              {(errorType === ERROR_TYPES.VALIDATION || errorType === ERROR_TYPES.UNKNOWN) && 'An unexpected error occurred. Please try again.'}
            </p>
            
            {/* Error ID */}
            <p className="text-xs text-gray-500 dark:text-gray-500 mb-6">
              Error ID: {this.state.errorId}
            </p>
            
            {/* Action Buttons */}
            <div className="space-y-3">
              {this.state.retryCount < 3 && (
                <EnterpriseButton
                  onClick={this.handleRetry}
                  variant="primary"
                  fullWidth
                  loading={this.state.isRecovering}
                  loadingText="Retrying..."
                >
                  Try Again
                </EnterpriseButton>
              )}
              
              <div className="flex space-x-3">
                <EnterpriseButton
                  onClick={this.handleGoHome}
                  variant="secondary"
                  size="sm"
                  fullWidth
                >
                  Go Home
                </EnterpriseButton>
                
                <EnterpriseButton
                  onClick={this.handleReload}
                  variant="ghost"
                  size="sm"
                  fullWidth
                >
                  Reload Page
                </EnterpriseButton>
              </div>
              
              <EnterpriseButton
                onClick={this.handleReportError}
                variant="ghost"
                size="sm"
                fullWidth
              >
                Report Error
              </EnterpriseButton>
            </div>
            
            {/* Development Info */}
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  Technical Details
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-auto max-h-40">
                  {error?.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }
    
    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export const withErrorBoundary = (Component, fallback) => {
  return function WrappedComponent(props) {
    return (
      <EnterpriseErrorBoundary fallback={fallback}>
        <Component {...props} />
      </EnterpriseErrorBoundary>
    );
  };
};

// Query Error Boundary wrapper
export const QueryErrorBoundary = ({ children, fallback }) => {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <EnterpriseErrorBoundary
          fallback={fallback || ((error, retry) => (
            <div className="p-6 text-center">
              <p className="text-red-600 mb-4">Failed to load data</p>
              <EnterpriseButton onClick={() => { reset(); retry(); }}>
                Try Again
              </EnterpriseButton>
            </div>
          ))}
        >
          {children}
        </EnterpriseErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  );
};

export default EnterpriseErrorBoundary;
