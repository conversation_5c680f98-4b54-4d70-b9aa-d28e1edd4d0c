/**
 * Cypress Configuration for E2E Testing
 * Enterprise-level end-to-end testing setup
 */

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    
    // Test file patterns
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    
    // Environment variables
    env: {
      apiUrl: 'http://localhost:5000/api',
      testUser: {
        email: '<EMAIL>',
        password: 'password123',
      },
    },
    
    setupNodeEvents(on, config) {
      // Accessibility testing
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },
        
        // Custom task for accessibility auditing
        accessibilityAudit(options) {
          // This would integrate with axe-core or similar
          return null;
        },
        
        // Performance testing
        performanceAudit(options) {
          // This would integrate with Lighthouse
          return null;
        },
      });
      
      // Code coverage
      require('@cypress/code-coverage/task')(on, config);
      
      return config;
    },
  },
  
  component: {
    devServer: {
      framework: 'create-react-app',
      bundler: 'webpack',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.js',
  },
  
  // Global configuration
  retries: {
    runMode: 2,
    openMode: 0,
  },
  
  // Browser configuration
  chromeWebSecurity: false,
  
  // Experimental features
  experimentalStudio: true,
  experimentalMemoryManagement: true,
});
