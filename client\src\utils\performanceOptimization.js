/**
 * Performance Optimization Utilities
 * Essential utilities for lazy loading, code splitting, and image optimization
 */

import { lazy } from 'react';

// Lazy loading utilities
export const LazyLoadingUtils = {
  // Lazy load images with intersection observer
  lazyLoadImages(selector = 'img[data-src]') {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
            observer.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      document.querySelectorAll(selector).forEach(img => {
        imageObserver.observe(img);
      });

      return imageObserver;
    }
  },

  // Preload critical resources
  preloadResource(href, as, type = null) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  },

  // Prefetch next page resources
  prefetchResource(href) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  }
};

// Code splitting utilities
export const CodeSplittingUtils = {
  // Lazy load React components
  lazyComponent(importFunc, fallback = null) {
    return lazy(() =>
      importFunc().catch(() => {
        // Return a fallback component
        return { default: () => fallback || <div>Failed to load component</div> };
      })
    );
  },

  // Dynamic import with retry logic
  async dynamicImport(importFunc, retries = 3) {
    for (let i = 0; i < retries; i++) {
      try {
        return await importFunc();
      } catch (error) {
        if (i === retries - 1) throw error;
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }
};



// Image optimization utilities
export const ImageOptimization = {
  // Generate responsive image srcset
  generateSrcSet(baseUrl, sizes = [320, 640, 768, 1024, 1280]) {
    return sizes.map(size => `${baseUrl}?w=${size} ${size}w`).join(', ');
  },

  // Optimize image loading
  optimizeImage(img, options = {}) {
    const {
      lazy = true,
      placeholder = true
    } = options;

    // Add loading attribute
    if (lazy) {
      img.loading = 'lazy';
    }

    // Add placeholder
    if (placeholder) {
      img.style.backgroundColor = '#f3f4f6';
      img.style.minHeight = '200px';
    }

    // Add error handling
    img.onerror = () => {
      img.src = '/images/placeholder.jpg'; // Fallback image
    };

    return img;
  },

  // Convert images to WebP format (client-side check)
  supportsWebP() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').startsWith('data:image/webp');
  }
};

// Export essential utilities only
export default {
  LazyLoadingUtils,
  CodeSplittingUtils,
  ImageOptimization
};
