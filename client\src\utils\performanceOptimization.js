/**
 * Performance Optimization Utilities for Enterprise-Level Frontend
 * Implements lazy loading, code splitting, image optimization, and performance monitoring
 */

import { lazy } from 'react';

// Performance monitoring utilities
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      loadTime: 0,
      renderTime: 0,
      interactionTime: 0,
      memoryUsage: 0,
      fps: 60,
    };
    this.observers = [];
    this.init();
  }

  init() {
    // Monitor Core Web Vitals
    this.observeWebVitals();
    
    // Monitor memory usage
    this.observeMemoryUsage();
    
    // Monitor FPS
    this.observeFPS();
    
    // Monitor long tasks
    this.observeLongTasks();
  }

  observeWebVitals() {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.lcp = lastEntry.startTime;
        console.log('📊 LCP:', lastEntry.startTime);
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          this.metrics.fid = entry.processingStart - entry.startTime;
          console.log('📊 FID:', this.metrics.fid);
        });
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.cls = clsValue;
        console.log('📊 CLS:', clsValue);
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    }
  }

  observeMemoryUsage() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        this.metrics.memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        };
      }, 5000);
    }
  }

  observeFPS() {
    let lastTime = performance.now();
    let frames = 0;
    
    const measureFPS = (currentTime) => {
      frames++;
      if (currentTime >= lastTime + 1000) {
        this.metrics.fps = Math.round((frames * 1000) / (currentTime - lastTime));
        frames = 0;
        lastTime = currentTime;
      }
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }

  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          console.warn('⚠️ Long task detected:', entry.duration + 'ms');
          this.metrics.longTasks = (this.metrics.longTasks || 0) + 1;
        });
      });
      
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Lazy loading utilities
export const LazyLoadingUtils = {
  // Lazy load images with intersection observer
  lazyLoadImages(selector = 'img[data-src]') {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
            observer.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      document.querySelectorAll(selector).forEach(img => {
        imageObserver.observe(img);
      });

      return imageObserver;
    }
  },

  // Preload critical resources
  preloadResource(href, as, type = null) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  },

  // Prefetch next page resources
  prefetchResource(href) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  }
};

// Code splitting utilities
export const CodeSplittingUtils = {
  // Lazy load React components
  lazyComponent(importFunc, fallback = null) {
    return lazy(() => 
      importFunc().catch(err => {
        console.error('Failed to load component:', err);
        // Return a fallback component
        return { default: () => fallback || <div>Failed to load component</div> };
      })
    );
  },

  // Dynamic import with retry logic
  async dynamicImport(importFunc, retries = 3) {
    for (let i = 0; i < retries; i++) {
      try {
        return await importFunc();
      } catch (error) {
        console.warn(`Import attempt ${i + 1} failed:`, error);
        if (i === retries - 1) throw error;
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }
};

// Bundle optimization utilities
export const BundleOptimization = {
  // Analyze bundle size
  analyzeBundleSize() {
    if (process.env.NODE_ENV === 'development') {
      console.log('📦 Bundle Analysis:');
      console.log('- React version:', React.version);
      console.log('- Environment:', process.env.NODE_ENV);
      
      // Estimate bundle size based on loaded modules
      const scripts = Array.from(document.scripts);
      let totalSize = 0;
      
      scripts.forEach(script => {
        if (script.src && script.src.includes('static/js/')) {
          fetch(script.src, { method: 'HEAD' })
            .then(response => {
              const size = response.headers.get('content-length');
              if (size) {
                totalSize += parseInt(size);
                console.log(`- ${script.src.split('/').pop()}: ${(size / 1024).toFixed(2)} KB`);
              }
            })
            .catch(() => {});
        }
      });
    }
  },

  // Tree shaking optimization
  optimizeImports() {
    console.log('🌳 Tree shaking optimizations applied');
    // This is mainly handled by webpack, but we can provide guidance
    return {
      recommendations: [
        'Use named imports instead of default imports',
        'Import only what you need from libraries',
        'Use dynamic imports for large dependencies',
        'Consider using lighter alternatives for heavy libraries'
      ]
    };
  }
};

// Image optimization utilities
export const ImageOptimization = {
  // Generate responsive image srcset
  generateSrcSet(baseUrl, sizes = [320, 640, 768, 1024, 1280]) {
    return sizes.map(size => `${baseUrl}?w=${size} ${size}w`).join(', ');
  },

  // Optimize image loading
  optimizeImage(img, options = {}) {
    const {
      quality = 80,
      format = 'webp',
      lazy = true,
      placeholder = true
    } = options;

    // Add loading attribute
    if (lazy) {
      img.loading = 'lazy';
    }

    // Add placeholder
    if (placeholder) {
      img.style.backgroundColor = '#f3f4f6';
      img.style.minHeight = '200px';
    }

    // Add error handling
    img.onerror = () => {
      console.warn('Failed to load image:', img.src);
      img.src = '/images/placeholder.jpg'; // Fallback image
    };

    return img;
  },

  // Convert images to WebP format (client-side check)
  supportsWebP() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }
};

// Performance budget monitoring
export const PerformanceBudget = {
  budgets: {
    lcp: 2500, // ms
    fid: 100,  // ms
    cls: 0.1,  // score
    bundleSize: 500, // KB
    imageSize: 200,  // KB per image
  },

  checkBudget(metrics) {
    const violations = [];
    
    if (metrics.lcp > this.budgets.lcp) {
      violations.push(`LCP exceeded: ${metrics.lcp}ms > ${this.budgets.lcp}ms`);
    }
    
    if (metrics.fid > this.budgets.fid) {
      violations.push(`FID exceeded: ${metrics.fid}ms > ${this.budgets.fid}ms`);
    }
    
    if (metrics.cls > this.budgets.cls) {
      violations.push(`CLS exceeded: ${metrics.cls} > ${this.budgets.cls}`);
    }

    if (violations.length > 0) {
      console.warn('⚠️ Performance budget violations:', violations);
    } else {
      console.log('✅ Performance budget met');
    }

    return violations;
  }
};

// Initialize performance monitoring
export const performanceMonitor = new PerformanceMonitor();

// Export default performance optimization suite
export default {
  PerformanceMonitor,
  LazyLoadingUtils,
  CodeSplittingUtils,
  BundleOptimization,
  ImageOptimization,
  PerformanceBudget,
  performanceMonitor
};
