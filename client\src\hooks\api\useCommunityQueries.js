/**
 * Community-related React Query hooks
 * Comprehensive community data management with real-time updates and optimistic UI
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { queryKeys, mutationKeys, invalidateQueries, optimisticUpdates } from '../../store';
import { communityAPI } from '../../services/api';

// Community posts with infinite scroll
export const useCommunityPosts = (filters = {}, options = {}) => {
  return useInfiniteQuery({
    queryKey: queryKeys.community.posts(filters),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await communityAPI.getPosts({
        ...filters,
        page: pageParam,
        limit: 10,
      });
      return response.data;
    },
    getNextPageParam: (lastPage) => {
      const { pagination } = lastPage;
      return pagination.hasNext ? pagination.page + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

// Single community post
export const useCommunityPost = (postId, options = {}) => {
  return useQuery({
    queryKey: queryKeys.community.post(postId),
    queryFn: async () => {
      const response = await communityAPI.getPost(postId);
      return response.data;
    },
    enabled: !!postId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// Post comments
export const usePostComments = (postId, options = {}) => {
  return useQuery({
    queryKey: queryKeys.community.comments(postId),
    queryFn: async () => {
      const response = await communityAPI.getComments(postId);
      return response.data;
    },
    enabled: !!postId,
    staleTime: 1 * 60 * 1000, // 1 minute (comments change frequently)
    ...options,
  });
};

// Community statistics
export const useCommunityStats = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.community.stats(),
    queryFn: async () => {
      const response = await communityAPI.getStats();
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

// Trending posts
export const useTrendingPosts = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.community.trending(),
    queryFn: async () => {
      const response = await communityAPI.getTrendingPosts();
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Auto-refresh every 10 minutes
    ...options,
  });
};

// Create post mutation
export const useCreatePost = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.createPost,
    mutationFn: async (postData) => {
      const response = await communityAPI.createPost(postData);
      return response.data;
    },
    onSuccess: (newPost) => {
      // Invalidate posts queries to show new post
      queryClient.invalidateQueries({ queryKey: queryKeys.community.posts() });
      queryClient.invalidateQueries({ queryKey: queryKeys.community.stats() });
      
      // Add new post to cache
      queryClient.setQueryData(queryKeys.community.post(newPost.id), newPost);
      
      toast.success('Post created successfully!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to create post';
      toast.error(message);
    },
  });
};

// Update post mutation
export const useUpdatePost = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.updatePost,
    mutationFn: async ({ postId, postData }) => {
      const response = await communityAPI.updatePost(postId, postData);
      return response.data;
    },
    onMutate: async ({ postId, postData }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.community.post(postId) });
      
      // Snapshot previous value
      const previousPost = queryClient.getQueryData(queryKeys.community.post(postId));
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.community.post(postId), (old) => ({
        ...old,
        ...postData,
        updatedAt: new Date().toISOString(),
      }));
      
      return { previousPost, postId };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      queryClient.setQueryData(
        queryKeys.community.post(context.postId),
        context.previousPost
      );
      
      const message = error.response?.data?.message || 'Failed to update post';
      toast.error(message);
    },
    onSuccess: () => {
      toast.success('Post updated successfully!');
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.community.post(variables.postId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.community.posts() });
    },
  });
};

// Delete post mutation
export const useDeletePost = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.deletePost,
    mutationFn: async (postId) => {
      await communityAPI.deletePost(postId);
      return postId;
    },
    onSuccess: (postId) => {
      // Remove post from cache
      queryClient.removeQueries({ queryKey: queryKeys.community.post(postId) });
      queryClient.removeQueries({ queryKey: queryKeys.community.comments(postId) });
      
      // Invalidate posts list
      queryClient.invalidateQueries({ queryKey: queryKeys.community.posts() });
      queryClient.invalidateQueries({ queryKey: queryKeys.community.stats() });
      
      toast.success('Post deleted successfully!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to delete post';
      toast.error(message);
    },
  });
};

// Vote on post mutation
export const useVotePost = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.votePost,
    mutationFn: async ({ postId, voteType }) => {
      const response = await communityAPI.votePost(postId, voteType);
      return { postId, voteType, result: response.data };
    },
    onMutate: async ({ postId, voteType }) => {
      // Optimistic update
      optimisticUpdates.votePost(queryClient, postId, voteType);
      
      return { postId, voteType };
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      queryClient.invalidateQueries({ queryKey: queryKeys.community.post(variables.postId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.community.posts() });
      
      const message = error.response?.data?.message || 'Failed to vote';
      toast.error(message);
    },
    onSuccess: (data) => {
      // Update with actual server data
      queryClient.setQueryData(queryKeys.community.post(data.postId), (old) => ({
        ...old,
        votes: data.result.votes,
        userVote: data.result.userVote,
      }));
    },
  });
};

// Create comment mutation
export const useCreateComment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.createComment,
    mutationFn: async ({ postId, content }) => {
      const response = await communityAPI.createComment(postId, content);
      return { postId, comment: response.data };
    },
    onMutate: async ({ postId, content }) => {
      // Create optimistic comment
      const optimisticComment = {
        id: `temp-${Date.now()}`,
        content,
        authorId: JSON.parse(localStorage.getItem('user') || '{}').id,
        authorName: JSON.parse(localStorage.getItem('user') || '{}').displayName,
        createdAt: new Date().toISOString(),
        votes: { up: 0, down: 0 },
        isOptimistic: true,
      };
      
      // Add to comments list
      optimisticUpdates.createComment(queryClient, postId, optimisticComment);
      
      return { postId, optimisticComment };
    },
    onError: (error, variables, context) => {
      // Remove optimistic comment
      queryClient.setQueryData(queryKeys.community.comments(context.postId), (old) => {
        if (!old) return old;
        return old.filter(comment => comment.id !== context.optimisticComment.id);
      });
      
      const message = error.response?.data?.message || 'Failed to create comment';
      toast.error(message);
    },
    onSuccess: ({ postId, comment }) => {
      // Replace optimistic comment with real one
      queryClient.setQueryData(queryKeys.community.comments(postId), (old) => {
        if (!old) return [comment];
        return old.map(c => c.isOptimistic ? comment : c);
      });
      
      // Update post comment count
      queryClient.setQueryData(queryKeys.community.post(postId), (old) => ({
        ...old,
        commentCount: (old?.commentCount || 0) + 1,
      }));
      
      toast.success('Comment added successfully!');
    },
  });
};

// Update comment mutation
export const useUpdateComment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.updateComment,
    mutationFn: async ({ commentId, content }) => {
      const response = await communityAPI.updateComment(commentId, content);
      return response.data;
    },
    onSuccess: (updatedComment) => {
      // Update comment in all relevant queries
      queryClient.setQueryData(
        queryKeys.community.comments(updatedComment.postId),
        (old) => {
          if (!old) return old;
          return old.map(comment =>
            comment.id === updatedComment.id ? updatedComment : comment
          );
        }
      );
      
      toast.success('Comment updated successfully!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to update comment';
      toast.error(message);
    },
  });
};

// Delete comment mutation
export const useDeleteComment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.community.deleteComment,
    mutationFn: async ({ commentId, postId }) => {
      await communityAPI.deleteComment(commentId);
      return { commentId, postId };
    },
    onSuccess: ({ commentId, postId }) => {
      // Remove comment from cache
      queryClient.setQueryData(queryKeys.community.comments(postId), (old) => {
        if (!old) return old;
        return old.filter(comment => comment.id !== commentId);
      });
      
      // Update post comment count
      queryClient.setQueryData(queryKeys.community.post(postId), (old) => ({
        ...old,
        commentCount: Math.max((old?.commentCount || 1) - 1, 0),
      }));
      
      toast.success('Comment deleted successfully!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to delete comment';
      toast.error(message);
    },
  });
};

// Search posts
export const useSearchPosts = (query, options = {}) => {
  return useQuery({
    queryKey: [...queryKeys.community.posts(), 'search', query],
    queryFn: async () => {
      if (!query?.trim()) return { posts: [], pagination: {} };
      
      const response = await communityAPI.searchPosts(query);
      return response.data;
    },
    enabled: !!query?.trim(),
    staleTime: 30 * 1000, // 30 seconds (search results change quickly)
    ...options,
  });
};

// Custom hook for real-time community updates
export const useRealtimeCommunityUpdates = () => {
  const queryClient = useQueryClient();
  
  // This would integrate with WebSocket or Server-Sent Events
  // For now, we'll use polling as a fallback
  useQuery({
    queryKey: ['community-updates'],
    queryFn: async () => {
      // Check for updates
      const response = await communityAPI.getUpdates();
      return response.data;
    },
    refetchInterval: 30 * 1000, // Poll every 30 seconds
    onSuccess: (updates) => {
      // Process updates and invalidate relevant queries
      if (updates.newPosts > 0) {
        queryClient.invalidateQueries({ queryKey: queryKeys.community.posts() });
      }
      
      if (updates.newComments > 0) {
        queryClient.invalidateQueries({ queryKey: queryKeys.community.comments() });
      }
    },
  });
};

export default {
  useCommunityPosts,
  useCommunityPost,
  usePostComments,
  useCommunityStats,
  useTrendingPosts,
  useCreatePost,
  useUpdatePost,
  useDeletePost,
  useVotePost,
  useCreateComment,
  useUpdateComment,
  useDeleteComment,
  useSearchPosts,
  useRealtimeCommunityUpdates,
};
