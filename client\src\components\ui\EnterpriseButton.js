/**
 * Enterprise-grade Button Component
 * Comprehensive button component with accessibility, animations, and extensive customization
 */

import React, { forwardRef, useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ANIMATION_CONFIG } from '../../utils/gsapAnimations';

// Button variants and sizes
const BUTTON_VARIANTS = {
  primary: 'btn-primary',
  secondary: 'btn-secondary',
  accent: 'btn-accent',
  success: 'bg-success-600 hover:bg-success-700 text-white',
  warning: 'bg-warning-600 hover:bg-warning-700 text-white',
  error: 'bg-error-600 hover:bg-error-700 text-white',
  ghost: 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300',
  outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white',
};

const BUTTON_SIZES = {
  xs: 'px-2 py-1 text-xs',
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
  xl: 'px-8 py-4 text-xl',
};

const BUTTON_SHAPES = {
  rounded: 'rounded-lg',
  pill: 'rounded-full',
  square: 'rounded-none',
  circle: 'rounded-full aspect-square',
};

/**
 * Enterprise Button Component
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Button variant (primary, secondary, accent, etc.)
 * @param {string} props.size - Button size (xs, sm, md, lg, xl)
 * @param {string} props.shape - Button shape (rounded, pill, square, circle)
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {boolean} props.loading - Whether button is in loading state
 * @param {boolean} props.fullWidth - Whether button takes full width
 * @param {React.ReactNode} props.leftIcon - Icon to display on the left
 * @param {React.ReactNode} props.rightIcon - Icon to display on the right
 * @param {string} props.loadingText - Text to display when loading
 * @param {boolean} props.animate - Whether to enable animations
 * @param {string} props.animationType - Type of animation (hover, press, glow)
 * @param {function} props.onClick - Click handler
 * @param {React.ReactNode} props.children - Button content
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.style - Inline styles
 * @param {string} props.ariaLabel - Accessibility label
 * @param {string} props.type - Button type (button, submit, reset)
 */
const EnterpriseButton = forwardRef(({
  variant = 'primary',
  size = 'md',
  shape = 'rounded',
  disabled = false,
  loading = false,
  fullWidth = false,
  leftIcon = null,
  rightIcon = null,
  loadingText = 'Loading...',
  animate = true,
  animationType = 'hover',
  onClick,
  children,
  className = '',
  style = {},
  ariaLabel,
  type = 'button',
  ...props
}, ref) => {
  const [isPressed, setIsPressed] = useState(false);
  const buttonRef = useRef(null);
  const rippleRef = useRef(null);

  // Combine refs
  const combinedRef = ref || buttonRef;

  // GSAP animations
  useEffect(() => {
    if (!animate || disabled) return;

    const button = combinedRef.current;
    if (!button) return;

    let hoverTl, pressTl;

    // Hover animation
    if (animationType === 'hover' || animationType === 'glow') {
      hoverTl = gsap.timeline({ paused: true });
      
      hoverTl.to(button, {
        scale: 1.05,
        y: -2,
        duration: ANIMATION_CONFIG.duration.fast,
        ease: ANIMATION_CONFIG.ease.smooth
      });

      if (animationType === 'glow') {
        hoverTl.to(button, {
          boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)',
          duration: ANIMATION_CONFIG.duration.fast
        }, 0);
      }

      button.addEventListener('mouseenter', () => hoverTl.play());
      button.addEventListener('mouseleave', () => hoverTl.reverse());
    }

    // Press animation
    if (animationType === 'press') {
      pressTl = gsap.timeline({ paused: true });
      
      pressTl.to(button, {
        scale: 0.95,
        duration: ANIMATION_CONFIG.duration.instant,
        ease: ANIMATION_CONFIG.ease.smooth
      });

      button.addEventListener('mousedown', () => pressTl.play());
      button.addEventListener('mouseup', () => pressTl.reverse());
      button.addEventListener('mouseleave', () => pressTl.reverse());
    }

    return () => {
      if (hoverTl) hoverTl.kill();
      if (pressTl) pressTl.kill();
    };
  }, [animate, animationType, disabled, combinedRef]);

  // Ripple effect
  const createRipple = (event) => {
    if (!animate || disabled) return;

    const button = event.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const ripple = document.createElement('span');
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      pointer-events: none;
      z-index: 1;
    `;

    button.appendChild(ripple);

    gsap.to(ripple, {
      scale: 2,
      opacity: 0,
      duration: 0.6,
      ease: 'power2.out',
      onComplete: () => {
        if (ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
      }
    });
  };

  // Handle click
  const handleClick = (event) => {
    if (disabled || loading) return;

    createRipple(event);
    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);

    if (onClick) {
      onClick(event);
    }
  };

  // Build CSS classes
  const baseClasses = [
    'btn', // Base button class from Tailwind config
    BUTTON_VARIANTS[variant] || BUTTON_VARIANTS.primary,
    BUTTON_SIZES[size] || BUTTON_SIZES.md,
    BUTTON_SHAPES[shape] || BUTTON_SHAPES.rounded,
    'relative overflow-hidden',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'transition-all duration-200',
    fullWidth ? 'w-full' : '',
    disabled || loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
    isPressed ? 'transform scale-95' : '',
    className
  ].filter(Boolean).join(' ');

  // Loading spinner component
  const LoadingSpinner = () => (
    <svg
      className="animate-spin h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  return (
    <button
      ref={combinedRef}
      type={type}
      className={baseClasses}
      style={style}
      disabled={disabled || loading}
      onClick={handleClick}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      {/* Left icon */}
      {leftIcon && !loading && (
        <span className="mr-2 flex-shrink-0">
          {leftIcon}
        </span>
      )}

      {/* Loading spinner */}
      {loading && (
        <span className="mr-2 flex-shrink-0">
          <LoadingSpinner />
        </span>
      )}

      {/* Button content */}
      <span className="flex-1">
        {loading ? loadingText : children}
      </span>

      {/* Right icon */}
      {rightIcon && !loading && (
        <span className="ml-2 flex-shrink-0">
          {rightIcon}
        </span>
      )}
    </button>
  );
});

EnterpriseButton.displayName = 'EnterpriseButton';

// Button group component
export const ButtonGroup = ({ 
  children, 
  orientation = 'horizontal',
  spacing = 'md',
  className = '',
  ...props 
}) => {
  const spacingClasses = {
    xs: orientation === 'horizontal' ? 'space-x-1' : 'space-y-1',
    sm: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
    md: orientation === 'horizontal' ? 'space-x-3' : 'space-y-3',
    lg: orientation === 'horizontal' ? 'space-x-4' : 'space-y-4',
    xl: orientation === 'horizontal' ? 'space-x-6' : 'space-y-6',
  };

  const groupClasses = [
    'flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    spacingClasses[spacing] || spacingClasses.md,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={groupClasses} {...props}>
      {children}
    </div>
  );
};

// Icon button component
export const IconButton = forwardRef(({
  icon,
  size = 'md',
  variant = 'ghost',
  ariaLabel,
  ...props
}, ref) => {
  return (
    <EnterpriseButton
      ref={ref}
      variant={variant}
      size={size}
      shape="circle"
      ariaLabel={ariaLabel}
      {...props}
    >
      {icon}
    </EnterpriseButton>
  );
});

IconButton.displayName = 'IconButton';

export default EnterpriseButton;
