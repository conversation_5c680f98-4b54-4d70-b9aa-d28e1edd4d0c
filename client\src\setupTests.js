/**
 * Jest Setup for Enterprise Testing
 * Configures testing environment with comprehensive mocks and utilities
 */

import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
// import { server } from './mocks/server'; // Commented out until MSW is properly configured

// Configure React Testing Library
configure({
  testIdAttribute: 'data-testid',
  asyncUtilTimeout: 5000,
});

// Mock GSAP for tests
jest.mock('gsap', () => ({
  gsap: {
    registerPlugin: jest.fn(),
    timeline: jest.fn(() => ({
      to: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      fromTo: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      play: jest.fn().mockReturnThis(),
      pause: jest.fn().mockReturnThis(),
      reverse: jest.fn().mockReturnThis(),
      kill: jest.fn().mockReturnThis(),
    })),
    to: jest.fn(),
    from: jest.fn(),
    fromTo: jest.fn(),
    set: jest.fn(),
    killTweensOf: jest.fn(),
    ticker: { fps: 60 },
  },
  ScrollTrigger: {
    create: jest.fn(),
    getAll: jest.fn(() => []),
    refresh: jest.fn(),
    update: jest.fn(),
  },
  TextPlugin: {},
  SplitText: jest.fn(() => ({
    chars: [],
    words: [],
    lines: [],
  })),
}));

// Mock Intersection Observer
global.IntersectionObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock Firebase
jest.mock('firebase/app', () => ({
  initializeApp: jest.fn(),
}));

jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({
    currentUser: null,
    onAuthStateChanged: jest.fn(),
    signInWithEmailAndPassword: jest.fn(),
    createUserWithEmailAndPassword: jest.fn(),
    signOut: jest.fn(),
    sendPasswordResetEmail: jest.fn(),
    sendEmailVerification: jest.fn(),
  })),
  onAuthStateChanged: jest.fn(),
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
  sendPasswordResetEmail: jest.fn(),
  sendEmailVerification: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(),
  collection: jest.fn(),
  doc: jest.fn(),
  getDoc: jest.fn(),
  getDocs: jest.fn(),
  addDoc: jest.fn(),
  updateDoc: jest.fn(),
  deleteDoc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  onSnapshot: jest.fn(),
}));

// Mock React Router
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useLocation: () => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
  }),
  useParams: () => ({}),
}));

// Mock performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
    memory: {
      usedJSHeapSize: 1000000,
      totalJSHeapSize: 2000000,
      jsHeapSizeLimit: 4000000,
    },
  },
});

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id));

// Mock Image constructor
global.Image = class {
  constructor() {
    setTimeout(() => {
      this.onload && this.onload();
    }, 100);
  }
};

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

// Mock fetch if not available
if (!global.fetch) {
  global.fetch = jest.fn(() =>
    Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
    })
  );
}

// Setup MSW server (commented out until properly configured)
// beforeAll(() => {
//   // Start the server before all tests
//   server.listen({
//     onUnhandledRequest: 'warn',
//   });
// });

afterEach(() => {
  // Reset any request handlers that are declared as a part of our tests
  // server.resetHandlers();

  // Clear all mocks
  jest.clearAllMocks();

  // Clear localStorage and sessionStorage
  localStorageMock.clear();
  sessionStorageMock.clear();
});

// afterAll(() => {
//   // Clean up after the tests are finished
//   server.close();
// });

// Global test utilities
global.testUtils = {
  // Wait for async operations
  waitFor: (callback, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        try {
          const result = callback();
          if (result) {
            resolve(result);
          } else if (Date.now() - startTime > timeout) {
            reject(new Error('Timeout waiting for condition'));
          } else {
            setTimeout(check, 100);
          }
        } catch (error) {
          if (Date.now() - startTime > timeout) {
            reject(error);
          } else {
            setTimeout(check, 100);
          }
        }
      };
      check();
    });
  },

  // Mock user for authentication tests
  mockUser: {
    uid: 'test-user-id',
    email: '<EMAIL>',
    displayName: 'Test User',
    emailVerified: true,
  },

  // Mock Firestore data
  mockFirestoreData: {
    users: [
      {
        id: 'user1',
        email: '<EMAIL>',
        displayName: 'User One',
        createdAt: new Date(),
      },
    ],
    posts: [
      {
        id: 'post1',
        title: 'Test Post',
        content: 'This is a test post',
        authorId: 'user1',
        createdAt: new Date(),
      },
    ],
  },
};

// Console error suppression for known issues
const originalError = console.error;
console.error = (...args) => {
  // Suppress specific warnings that are expected in tests
  const suppressedWarnings = [
    'Warning: ReactDOM.render is deprecated',
    'Warning: componentWillReceiveProps has been renamed',
    'Warning: componentWillMount has been renamed',
  ];

  const message = args[0];
  if (typeof message === 'string' && suppressedWarnings.some(warning => message.includes(warning))) {
    return;
  }

  originalError.call(console, ...args);
};

// Add custom matchers
expect.extend({
  toBeAccessible(received) {
    // Custom matcher for accessibility testing
    const hasAriaLabel = received.getAttribute('aria-label');
    const hasRole = received.getAttribute('role');
    const hasTabIndex = received.getAttribute('tabindex');
    
    const pass = hasAriaLabel || hasRole || hasTabIndex !== null;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be accessible`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be accessible (have aria-label, role, or tabindex)`,
        pass: false,
      };
    }
  },

  toHaveValidContrast(received) {
    // Custom matcher for color contrast testing
    const style = window.getComputedStyle(received);
    const color = style.color;
    const backgroundColor = style.backgroundColor;
    
    // Simplified contrast check (in real implementation, use proper contrast calculation)
    const pass = color !== backgroundColor;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to have valid contrast`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to have valid color contrast`,
        pass: false,
      };
    }
  },
});

console.log('🧪 Test environment configured with enterprise-level setup');
