/**
 * Enterprise Button Component Tests
 * Comprehensive testing for accessibility, functionality, and performance
 */

import React from 'react';
import { renderWithProviders, screen, fireEvent, waitFor } from '../../../utils/testUtils';
import EnterpriseButton, { ButtonGroup, IconButton } from '../EnterpriseButton';

describe('EnterpriseButton', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    it('renders with default props', () => {
      renderWithProviders(<EnterpriseButton>Click me</EnterpriseButton>);
      
      const button = screen.getByRole('button', { name: /click me/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('btn', 'btn-primary');
    });

    it('renders with different variants', () => {
      const variants = ['primary', 'secondary', 'accent', 'success', 'warning', 'error'];
      
      variants.forEach(variant => {
        const { unmount } = renderWithProviders(
          <EnterpriseButton variant={variant}>Test</EnterpriseButton>
        );
        
        const button = screen.getByRole('button');
        expect(button).toHaveClass(`btn-${variant}`);
        
        unmount();
      });
    });

    it('renders with different sizes', () => {
      const sizes = ['xs', 'sm', 'md', 'lg', 'xl'];
      
      sizes.forEach(size => {
        const { unmount } = renderWithProviders(
          <EnterpriseButton size={size}>Test</EnterpriseButton>
        );
        
        const button = screen.getByRole('button');
        expect(button).toHaveClass('btn'); // Base class should always be present
        
        unmount();
      });
    });

    it('renders with icons', () => {
      const LeftIcon = () => <span data-testid="left-icon">←</span>;
      const RightIcon = () => <span data-testid="right-icon">→</span>;
      
      renderWithProviders(
        <EnterpriseButton leftIcon={<LeftIcon />} rightIcon={<RightIcon />}>
          With Icons
        </EnterpriseButton>
      );
      
      expect(screen.getByTestId('left-icon')).toBeInTheDocument();
      expect(screen.getByTestId('right-icon')).toBeInTheDocument();
    });
  });

  // Accessibility tests
  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(
        <EnterpriseButton ariaLabel="Custom label">Button</EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Custom label');
    });

    it('is keyboard accessible', async () => {
      const handleClick = jest.fn();
      renderWithProviders(
        <EnterpriseButton onClick={handleClick}>Keyboard Test</EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      
      // Test Enter key
      fireEvent.keyDown(button, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      // Test Space key
      fireEvent.keyDown(button, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    it('handles disabled state correctly', () => {
      const handleClick = jest.fn();
      renderWithProviders(
        <EnterpriseButton disabled onClick={handleClick}>
          Disabled Button
        </EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('aria-disabled', 'true');
      
      fireEvent.click(button);
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('handles loading state correctly', () => {
      renderWithProviders(
        <EnterpriseButton loading loadingText="Loading...">
          Submit
        </EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('aria-busy', 'true');
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  // Interaction tests
  describe('Interactions', () => {
    it('calls onClick handler when clicked', () => {
      const handleClick = jest.fn();
      renderWithProviders(
        <EnterpriseButton onClick={handleClick}>Click me</EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not call onClick when disabled', () => {
      const handleClick = jest.fn();
      renderWithProviders(
        <EnterpriseButton disabled onClick={handleClick}>
          Disabled
        </EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('does not call onClick when loading', () => {
      const handleClick = jest.fn();
      renderWithProviders(
        <EnterpriseButton loading onClick={handleClick}>
          Loading
        </EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  // Animation tests
  describe('Animations', () => {
    it('applies animation classes when animate is true', () => {
      renderWithProviders(
        <EnterpriseButton animate={true}>Animated</EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('transition-all');
    });

    it('skips animations when animate is false', () => {
      renderWithProviders(
        <EnterpriseButton animate={false}>Not Animated</EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      // Should still have base classes but not animation-specific ones
      expect(button).toHaveClass('btn');
    });
  });

  // Performance tests
  describe('Performance', () => {
    it('renders quickly', async () => {
      const startTime = performance.now();
      
      renderWithProviders(<EnterpriseButton>Performance Test</EnterpriseButton>);
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render in less than 50ms
      expect(renderTime).toBeLessThan(50);
    });

    it('handles multiple rapid clicks', async () => {
      const handleClick = jest.fn();
      renderWithProviders(
        <EnterpriseButton onClick={handleClick}>Rapid Click</EnterpriseButton>
      );
      
      const button = screen.getByRole('button');
      
      // Simulate rapid clicking
      for (let i = 0; i < 10; i++) {
        fireEvent.click(button);
      }
      
      expect(handleClick).toHaveBeenCalledTimes(10);
    });
  });
});

describe('ButtonGroup', () => {
  it('renders children with proper spacing', () => {
    renderWithProviders(
      <ButtonGroup>
        <EnterpriseButton>Button 1</EnterpriseButton>
        <EnterpriseButton>Button 2</EnterpriseButton>
        <EnterpriseButton>Button 3</EnterpriseButton>
      </ButtonGroup>
    );
    
    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(3);
  });

  it('applies correct orientation classes', () => {
    const { container } = renderWithProviders(
      <ButtonGroup orientation="vertical">
        <EnterpriseButton>Button 1</EnterpriseButton>
        <EnterpriseButton>Button 2</EnterpriseButton>
      </ButtonGroup>
    );
    
    const group = container.firstChild;
    expect(group).toHaveClass('flex-col');
  });
});

describe('IconButton', () => {
  it('renders icon correctly', () => {
    const TestIcon = () => <span data-testid="test-icon">🔥</span>;
    
    renderWithProviders(
      <IconButton icon={<TestIcon />} ariaLabel="Fire icon" />
    );
    
    const button = screen.getByRole('button', { name: /fire icon/i });
    const icon = screen.getByTestId('test-icon');
    
    expect(button).toBeInTheDocument();
    expect(icon).toBeInTheDocument();
  });

  it('has circular shape by default', () => {
    const TestIcon = () => <span>🔥</span>;
    
    renderWithProviders(
      <IconButton icon={<TestIcon />} ariaLabel="Test" />
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('rounded-full');
  });
});

// Integration tests
describe('Button Integration', () => {
  it('works within forms', async () => {
    const handleSubmit = jest.fn(e => e.preventDefault());
    
    renderWithProviders(
      <form onSubmit={handleSubmit}>
        <EnterpriseButton type="submit">Submit Form</EnterpriseButton>
      </form>
    );
    
    const button = screen.getByRole('button', { name: /submit form/i });
    fireEvent.click(button);
    
    expect(handleSubmit).toHaveBeenCalled();
  });

  it('maintains focus management', () => {
    renderWithProviders(
      <div>
        <EnterpriseButton>Button 1</EnterpriseButton>
        <EnterpriseButton>Button 2</EnterpriseButton>
      </div>
    );
    
    const buttons = screen.getAllByRole('button');
    
    buttons[0].focus();
    expect(document.activeElement).toBe(buttons[0]);
    
    // Tab to next button
    fireEvent.keyDown(buttons[0], { key: 'Tab' });
    buttons[1].focus();
    expect(document.activeElement).toBe(buttons[1]);
  });
});
