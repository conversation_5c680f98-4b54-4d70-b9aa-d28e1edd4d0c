/**
 * Enterprise-grade Input Component
 * Comprehensive input component with validation, accessibility, and animations
 */

import React, { forwardRef, useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ANIMATION_CONFIG } from '../../utils/gsapAnimations';

// Input variants and sizes
const INPUT_VARIANTS = {
  default: 'border-gray-300 focus:border-primary-500 focus:ring-primary-500',
  success: 'border-success-300 focus:border-success-500 focus:ring-success-500',
  warning: 'border-warning-300 focus:border-warning-500 focus:ring-warning-500',
  error: 'border-error-300 focus:border-error-500 focus:ring-error-500',
  ghost: 'border-transparent bg-gray-50 focus:bg-white focus:border-primary-500',
};

const INPUT_SIZES = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-4 py-3 text-lg',
  xl: 'px-6 py-4 text-xl',
};

/**
 * Enterprise Input Component
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Input variant (default, success, warning, error, ghost)
 * @param {string} props.size - Input size (sm, md, lg, xl)
 * @param {string} props.label - Input label
 * @param {string} props.placeholder - Input placeholder
 * @param {string} props.helperText - Helper text below input
 * @param {string} props.errorMessage - Error message
 * @param {boolean} props.required - Whether input is required
 * @param {boolean} props.disabled - Whether input is disabled
 * @param {boolean} props.readOnly - Whether input is read-only
 * @param {React.ReactNode} props.leftIcon - Icon to display on the left
 * @param {React.ReactNode} props.rightIcon - Icon to display on the right
 * @param {React.ReactNode} props.leftAddon - Addon to display on the left
 * @param {React.ReactNode} props.rightAddon - Addon to display on the right
 * @param {boolean} props.animate - Whether to enable animations
 * @param {function} props.onValidate - Custom validation function
 * @param {Object} props.validation - Validation rules
 * @param {boolean} props.showCharCount - Whether to show character count
 * @param {number} props.maxLength - Maximum character length
 * @param {string} props.type - Input type
 * @param {string} props.value - Input value
 * @param {function} props.onChange - Change handler
 * @param {function} props.onFocus - Focus handler
 * @param {function} props.onBlur - Blur handler
 * @param {string} props.className - Additional CSS classes
 */
const EnterpriseInput = forwardRef(({
  variant = 'default',
  size = 'md',
  label,
  placeholder,
  helperText,
  errorMessage,
  required = false,
  disabled = false,
  readOnly = false,
  leftIcon,
  rightIcon,
  leftAddon,
  rightAddon,
  animate = true,
  onValidate,
  validation = {},
  showCharCount = false,
  maxLength,
  type = 'text',
  value = '',
  onChange,
  onFocus,
  onBlur,
  className = '',
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [validationMessage, setValidationMessage] = useState('');
  const [charCount, setCharCount] = useState(value.length);
  
  const inputRef = useRef(null);
  const labelRef = useRef(null);
  const containerRef = useRef(null);

  // Combine refs
  const combinedRef = ref || inputRef;

  // Validation logic
  const validateInput = (inputValue) => {
    if (onValidate) {
      const result = onValidate(inputValue);
      setIsValid(result.isValid);
      setValidationMessage(result.message || '');
      return result.isValid;
    }

    // Built-in validation rules
    if (required && !inputValue.trim()) {
      setIsValid(false);
      setValidationMessage('This field is required');
      return false;
    }

    if (validation.minLength && inputValue.length < validation.minLength) {
      setIsValid(false);
      setValidationMessage(`Minimum ${validation.minLength} characters required`);
      return false;
    }

    if (validation.maxLength && inputValue.length > validation.maxLength) {
      setIsValid(false);
      setValidationMessage(`Maximum ${validation.maxLength} characters allowed`);
      return false;
    }

    if (validation.pattern && !validation.pattern.test(inputValue)) {
      setIsValid(false);
      setValidationMessage(validation.patternMessage || 'Invalid format');
      return false;
    }

    if (type === 'email' && inputValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(inputValue)) {
      setIsValid(false);
      setValidationMessage('Please enter a valid email address');
      return false;
    }

    setIsValid(true);
    setValidationMessage('');
    return true;
  };

  // GSAP animations
  useEffect(() => {
    if (!animate) return;

    const container = containerRef.current;
    const labelElement = labelRef.current;
    
    if (!container) return;

    let focusTl;

    if (isFocused) {
      focusTl = gsap.timeline();
      
      // Container glow effect
      focusTl.to(container, {
        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
        duration: ANIMATION_CONFIG.duration.fast,
        ease: ANIMATION_CONFIG.ease.smooth
      });

      // Floating label animation
      if (labelElement && label) {
        focusTl.to(labelElement, {
          scale: 0.85,
          y: -8,
          color: '#3b82f6',
          duration: ANIMATION_CONFIG.duration.fast,
          ease: ANIMATION_CONFIG.ease.smooth
        }, 0);
      }
    } else {
      focusTl = gsap.timeline();
      
      focusTl.to(container, {
        boxShadow: '0 0 0 0px rgba(59, 130, 246, 0)',
        duration: ANIMATION_CONFIG.duration.fast,
        ease: ANIMATION_CONFIG.ease.smooth
      });

      if (labelElement && label && !value) {
        focusTl.to(labelElement, {
          scale: 1,
          y: 0,
          color: '#6b7280',
          duration: ANIMATION_CONFIG.duration.fast,
          ease: ANIMATION_CONFIG.ease.smooth
        }, 0);
      }
    }

    return () => {
      if (focusTl) focusTl.kill();
    };
  }, [isFocused, animate, label, value]);

  // Handle input change
  const handleChange = (event) => {
    const newValue = event.target.value;
    setCharCount(newValue.length);
    
    if (onChange) {
      onChange(event);
    }

    // Validate on change if there's an error
    if (!isValid || validationMessage) {
      validateInput(newValue);
    }
  };

  // Handle focus
  const handleFocus = (event) => {
    setIsFocused(true);
    if (onFocus) {
      onFocus(event);
    }
  };

  // Handle blur
  const handleBlur = (event) => {
    setIsFocused(false);
    validateInput(event.target.value);
    if (onBlur) {
      onBlur(event);
    }
  };

  // Determine current variant
  const currentVariant = errorMessage || !isValid ? 'error' : variant;

  // Build CSS classes
  const containerClasses = [
    'relative',
    disabled ? 'opacity-50 cursor-not-allowed' : '',
    className
  ].filter(Boolean).join(' ');

  const inputClasses = [
    'input', // Base input class from Tailwind config
    INPUT_VARIANTS[currentVariant] || INPUT_VARIANTS.default,
    INPUT_SIZES[size] || INPUT_SIZES.md,
    'w-full rounded-lg border transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-0',
    leftIcon || leftAddon ? 'pl-10' : '',
    rightIcon || rightAddon ? 'pr-10' : '',
    disabled ? 'cursor-not-allowed' : '',
    readOnly ? 'cursor-default' : '',
  ].filter(Boolean).join(' ');

  const labelClasses = [
    'absolute left-3 transition-all duration-200 pointer-events-none',
    isFocused || value ? 'top-0 text-xs' : 'top-1/2 transform -translate-y-1/2 text-base',
    currentVariant === 'error' ? 'text-error-600' : 'text-gray-500',
    required ? "after:content-['*'] after:text-error-500 after:ml-1" : '',
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Label */}
      {label && (
        <label
          ref={labelRef}
          className={labelClasses}
          htmlFor={props.id}
        >
          {label}
        </label>
      )}

      {/* Input container */}
      <div ref={containerRef} className="relative">
        {/* Left addon */}
        {leftAddon && (
          <div className="absolute left-0 top-0 h-full flex items-center px-3 bg-gray-50 border-r border-gray-300 rounded-l-lg">
            {leftAddon}
          </div>
        )}

        {/* Left icon */}
        {leftIcon && !leftAddon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {leftIcon}
          </div>
        )}

        {/* Input */}
        <input
          ref={combinedRef}
          type={type}
          className={inputClasses}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          readOnly={readOnly}
          maxLength={maxLength}
          aria-invalid={!isValid}
          aria-describedby={
            [
              helperText && `${props.id}-helper`,
              (errorMessage || validationMessage) && `${props.id}-error`
            ].filter(Boolean).join(' ') || undefined
          }
          {...props}
        />

        {/* Right icon */}
        {rightIcon && !rightAddon && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {rightIcon}
          </div>
        )}

        {/* Right addon */}
        {rightAddon && (
          <div className="absolute right-0 top-0 h-full flex items-center px-3 bg-gray-50 border-l border-gray-300 rounded-r-lg">
            {rightAddon}
          </div>
        )}
      </div>

      {/* Helper text, error message, and character count */}
      <div className="mt-1 flex justify-between items-start">
        <div className="flex-1">
          {/* Helper text */}
          {helperText && !errorMessage && !validationMessage && (
            <p id={`${props.id}-helper`} className="text-sm text-gray-500">
              {helperText}
            </p>
          )}

          {/* Error message */}
          {(errorMessage || validationMessage) && (
            <p id={`${props.id}-error`} className="text-sm text-error-600">
              {errorMessage || validationMessage}
            </p>
          )}
        </div>

        {/* Character count */}
        {showCharCount && maxLength && (
          <p className={`text-xs ml-2 ${charCount > maxLength * 0.9 ? 'text-warning-600' : 'text-gray-400'}`}>
            {charCount}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
});

EnterpriseInput.displayName = 'EnterpriseInput';

// Input group component for multiple related inputs
export const InputGroup = ({
  children,
  orientation = 'vertical',
  spacing = 'md',
  className = '',
  ...props
}) => {
  const spacingClasses = {
    xs: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
    sm: orientation === 'horizontal' ? 'space-x-3' : 'space-y-3',
    md: orientation === 'horizontal' ? 'space-x-4' : 'space-y-4',
    lg: orientation === 'horizontal' ? 'space-x-6' : 'space-y-6',
  };

  const groupClasses = [
    'flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    spacingClasses[spacing] || spacingClasses.md,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={groupClasses} {...props}>
      {children}
    </div>
  );
};

// Search input component
export const SearchInput = forwardRef(({
  onSearch,
  searchIcon = (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
  ),
  clearable = true,
  ...props
}, ref) => {
  const [searchValue, setSearchValue] = useState(props.value || '');

  const handleChange = (event) => {
    const newValue = event.target.value;
    setSearchValue(newValue);

    if (props.onChange) {
      props.onChange(event);
    }

    // Debounced search
    if (onSearch) {
      clearTimeout(window.searchTimeout);
      window.searchTimeout = setTimeout(() => {
        onSearch(newValue);
      }, 300);
    }
  };

  const handleClear = () => {
    setSearchValue('');
    if (onSearch) {
      onSearch('');
    }
    if (props.onChange) {
      props.onChange({ target: { value: '' } });
    }
  };

  return (
    <EnterpriseInput
      ref={ref}
      type="search"
      leftIcon={searchIcon}
      rightIcon={clearable && searchValue && (
        <button
          type="button"
          onClick={handleClear}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
      value={searchValue}
      onChange={handleChange}
      {...props}
    />
  );
});

SearchInput.displayName = 'SearchInput';

export default EnterpriseInput;
