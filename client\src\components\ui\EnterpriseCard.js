/**
 * Enterprise-grade Card Component
 * Comprehensive card component with animations, interactions, and accessibility
 */

import React, { forwardRef, useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ANIMATION_CONFIG } from '../../utils/gsapAnimations';

// Card variants and sizes
const CARD_VARIANTS = {
  default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
  elevated: 'bg-white dark:bg-gray-800 shadow-lg border-0',
  outlined: 'bg-transparent border-2 border-gray-300 dark:border-gray-600',
  filled: 'bg-gray-50 dark:bg-gray-700 border-0',
  glass: 'glass backdrop-blur-md bg-white/75 dark:bg-gray-800/75 border border-white/20',
  gradient: 'bg-gradient-to-br from-primary-500 to-accent-500 text-white border-0',
};

const CARD_SIZES = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10',
};

const CARD_RADIUSES = {
  none: 'rounded-none',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  '2xl': 'rounded-2xl',
  full: 'rounded-full',
};

/**
 * Enterprise Card Component
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Card variant (default, elevated, outlined, filled, glass, gradient)
 * @param {string} props.size - Card padding size (sm, md, lg, xl)
 * @param {string} props.radius - Card border radius (none, sm, md, lg, xl, 2xl, full)
 * @param {boolean} props.hoverable - Whether card has hover effects
 * @param {boolean} props.clickable - Whether card is clickable
 * @param {boolean} props.selectable - Whether card can be selected
 * @param {boolean} props.selected - Whether card is currently selected
 * @param {boolean} props.loading - Whether card is in loading state
 * @param {boolean} props.animate - Whether to enable animations
 * @param {string} props.animationType - Type of animation (hover, lift, glow, scale)
 * @param {React.ReactNode} props.header - Card header content
 * @param {React.ReactNode} props.footer - Card footer content
 * @param {React.ReactNode} props.actions - Card action buttons
 * @param {React.ReactNode} props.media - Card media content (image, video)
 * @param {function} props.onClick - Click handler
 * @param {function} props.onSelect - Selection handler
 * @param {React.ReactNode} props.children - Card content
 * @param {string} props.className - Additional CSS classes
 */
const EnterpriseCard = forwardRef(({
  variant = 'default',
  size = 'md',
  radius = 'lg',
  hoverable = false,
  clickable = false,
  selectable = false,
  selected = false,
  loading = false,
  animate = true,
  animationType = 'hover',
  header,
  footer,
  actions,
  media,
  onClick,
  onSelect,
  children,
  className = '',
  ...props
}, ref) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const cardRef = useRef(null);

  // Combine refs
  const combinedRef = ref || cardRef;

  // GSAP animations
  useEffect(() => {
    if (!animate || loading) return;

    const card = combinedRef.current;
    if (!card) return;

    let hoverTl, pressTl;

    // Hover animations
    if (hoverable || clickable) {
      hoverTl = gsap.timeline({ paused: true });

      switch (animationType) {
        case 'lift':
          hoverTl.to(card, {
            y: -8,
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            duration: ANIMATION_CONFIG.duration.fast,
            ease: ANIMATION_CONFIG.ease.smooth
          });
          break;

        case 'scale':
          hoverTl.to(card, {
            scale: 1.02,
            duration: ANIMATION_CONFIG.duration.fast,
            ease: ANIMATION_CONFIG.ease.smooth
          });
          break;

        case 'glow':
          hoverTl.to(card, {
            boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',
            duration: ANIMATION_CONFIG.duration.fast,
            ease: ANIMATION_CONFIG.ease.smooth
          });
          break;

        default: // hover
          hoverTl.to(card, {
            y: -2,
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            duration: ANIMATION_CONFIG.duration.fast,
            ease: ANIMATION_CONFIG.ease.smooth
          });
      }

      card.addEventListener('mouseenter', () => {
        setIsHovered(true);
        hoverTl.play();
      });

      card.addEventListener('mouseleave', () => {
        setIsHovered(false);
        hoverTl.reverse();
      });
    }

    // Press animation for clickable cards
    if (clickable) {
      pressTl = gsap.timeline({ paused: true });
      
      pressTl.to(card, {
        scale: 0.98,
        duration: ANIMATION_CONFIG.duration.instant,
        ease: ANIMATION_CONFIG.ease.smooth
      });

      card.addEventListener('mousedown', () => {
        setIsPressed(true);
        pressTl.play();
      });

      card.addEventListener('mouseup', () => {
        setIsPressed(false);
        pressTl.reverse();
      });

      card.addEventListener('mouseleave', () => {
        setIsPressed(false);
        pressTl.reverse();
      });
    }

    return () => {
      if (hoverTl) hoverTl.kill();
      if (pressTl) pressTl.kill();
    };
  }, [animate, animationType, hoverable, clickable, loading, combinedRef]);

  // Handle click
  const handleClick = (event) => {
    if (loading) return;

    if (selectable && onSelect) {
      onSelect(!selected);
    }

    if (onClick) {
      onClick(event);
    }
  };

  // Handle keyboard interaction
  const handleKeyDown = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick(event);
    }
  };

  // Build CSS classes
  const baseClasses = [
    'card', // Base card class from Tailwind config
    CARD_VARIANTS[variant] || CARD_VARIANTS.default,
    CARD_SIZES[size] || CARD_SIZES.md,
    CARD_RADIUSES[radius] || CARD_RADIUSES.lg,
    'transition-all duration-200',
    clickable || selectable ? 'cursor-pointer' : '',
    selected ? 'ring-2 ring-primary-500 ring-offset-2' : '',
    loading ? 'opacity-50 pointer-events-none' : '',
    className
  ].filter(Boolean).join(' ');

  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="animate-pulse space-y-4">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
      </div>
    </div>
  );

  return (
    <div
      ref={combinedRef}
      className={baseClasses}
      onClick={clickable || selectable ? handleClick : undefined}
      onKeyDown={clickable || selectable ? handleKeyDown : undefined}
      tabIndex={clickable || selectable ? 0 : undefined}
      role={clickable ? 'button' : selectable ? 'checkbox' : undefined}
      aria-checked={selectable ? selected : undefined}
      aria-disabled={loading}
      {...props}
    >
      {/* Selection indicator */}
      {selectable && (
        <div className="absolute top-4 right-4">
          <div className={`w-5 h-5 rounded border-2 transition-all duration-200 ${
            selected 
              ? 'bg-primary-500 border-primary-500' 
              : 'border-gray-300 dark:border-gray-600'
          }`}>
            {selected && (
              <svg className="w-3 h-3 text-white m-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        </div>
      )}

      {/* Media */}
      {media && (
        <div className={`-m-${size} mb-${size} ${CARD_RADIUSES[radius]} overflow-hidden`}>
          {media}
        </div>
      )}

      {/* Header */}
      {header && (
        <div className={`-m-${size} mx-${size} mt-${size} mb-4 pb-4 border-b border-gray-200 dark:border-gray-700`}>
          {header}
        </div>
      )}

      {/* Content */}
      <div className="flex-1">
        {loading ? <LoadingSkeleton /> : children}
      </div>

      {/* Footer */}
      {footer && (
        <div className={`-m-${size} mx-${size} mb-${size} mt-4 pt-4 border-t border-gray-200 dark:border-gray-700`}>
          {footer}
        </div>
      )}

      {/* Actions */}
      {actions && (
        <div className={`-m-${size} mx-${size} mb-${size} mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2`}>
          {actions}
        </div>
      )}
    </div>
  );
});

EnterpriseCard.displayName = 'EnterpriseCard';

// Card header component
export const CardHeader = ({ title, subtitle, actions, className = '', ...props }) => (
  <div className={`flex items-start justify-between ${className}`} {...props}>
    <div className="flex-1">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {title}
        </h3>
      )}
      {subtitle && (
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {subtitle}
        </p>
      )}
    </div>
    {actions && (
      <div className="flex items-center space-x-2 ml-4">
        {actions}
      </div>
    )}
  </div>
);

// Card grid component
export const CardGrid = ({ 
  children, 
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 6,
  className = '',
  ...props 
}) => {
  const gridClasses = [
    'grid',
    `grid-cols-${columns.sm || 1}`,
    `md:grid-cols-${columns.md || 2}`,
    `lg:grid-cols-${columns.lg || 3}`,
    `xl:grid-cols-${columns.xl || 4}`,
    `gap-${gap}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={gridClasses} {...props}>
      {children}
    </div>
  );
};

export default EnterpriseCard;
