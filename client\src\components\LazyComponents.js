/**
 * Lazy-loaded components for code splitting and performance optimization
 * This file centralizes all lazy imports for better bundle management
 */

import { lazy, Suspense } from 'react';
import { CodeSplittingUtils } from '../utils/performanceOptimization';

// Loading fallback components
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
  </div>
);

const LoadingCard = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 dark:bg-gray-700 rounded-xl h-48 w-full"></div>
  </div>
);

const LoadingPage = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <p className="text-gray-600 dark:text-gray-400">Loading...</p>
    </div>
  </div>
);

// Route-based code splitting helper (moved to top to avoid hoisting issues)
export const createLazyRoute = (importFunc, fallback = <LoadingPage />) => {
  const LazyComponent = lazy(importFunc);

  return function LazyRoute(props) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
};

// Lazy-loaded page components
export const LazyHomePage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/HomePage'),
  <LoadingPage />
);

export const LazyDashboardPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/DashboardPage'),
  <LoadingPage />
);

export const LazyCheckLinkPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/CheckLinkPage'),
  <LoadingPage />
);

export const LazyCommunityPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/CommunityPage'),
  <LoadingPage />
);

export const LazyProfilePage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/ProfilePage'),
  <LoadingPage />
);

export const LazySettingsPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/SettingsPage'),
  <LoadingPage />
);

export const LazyAdminPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/AdminDashboard'),
  <LoadingPage />
);

// Lazy-loaded feature components
// Use existing TrendingArticles component
export const LazyTrendingArticles = CodeSplittingUtils.lazyComponent(
  () => import('./TrendingArticles'),
  <LoadingCard />
);

// Placeholder components for missing features
export const LazyCommunityPreview = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Community Preview</h3>
    <p className="text-gray-600">Community preview component will be implemented here.</p>
  </div>
);

export const LazyLatestNews = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Latest News</h3>
    <p className="text-gray-600">Latest news component will be implemented here.</p>
  </div>
);

export const LazyAnimatedStats = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Animated Stats</h3>
    <p className="text-gray-600">Animated stats component will be implemented here.</p>
  </div>
);

export const LazyFactCheckWidget = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Fact Check Widget</h3>
    <p className="text-gray-600">Fact check widget component will be implemented here.</p>
  </div>
);

export const LazyChatInterface = createLazyRoute(() => import('../pages/ChatPage'));

// Lazy-loaded UI components
// Placeholder components for missing UI components
export const LazyDataTable = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Data Table</h3>
    <p className="text-gray-600">Data table component will be implemented here.</p>
  </div>
);

export const LazyChart = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Chart</h3>
    <p className="text-gray-600">Chart component will be implemented here.</p>
  </div>
);

export const LazyImageGallery = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Image Gallery</h3>
    <p className="text-gray-600">Image gallery component will be implemented here.</p>
  </div>
);

export const LazyVideoPlayer = () => (
  <div className="p-4 border rounded-lg">
    <h3 className="text-lg font-semibold mb-2">Video Player</h3>
    <p className="text-gray-600">Video player component will be implemented here.</p>
  </div>
);

// Use existing auth pages instead of separate form components
export const LazyLoginForm = createLazyRoute(() => import('../pages/LoginPage'));
export const LazyRegisterForm = createLazyRoute(() => import('../pages/ModernRegisterPage'));
export const LazyForgotPasswordForm = createLazyRoute(() => import('../pages/ForgotPasswordPage'));

// Higher-order component for lazy loading with error boundary
export const withLazyLoading = (LazyComponent, fallback = <LoadingSpinner />) => {
  return function LazyWrapper(props) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
};



// Preload components for better UX
export const preloadComponents = {
  dashboard: () => import('../pages/DashboardPage'),
  community: () => import('../pages/CommunityPage'),
  checkLink: () => import('../pages/CheckLinkPage'),
  profile: () => import('../pages/ProfilePage'),
  chat: () => import('../pages/ChatPage'),
};

// Preload critical components on user interaction
export const preloadOnHover = (componentKey) => {
  return {
    onMouseEnter: () => {
      if (preloadComponents[componentKey]) {
        preloadComponents[componentKey]();
      }
    }
  };
};

// Bundle splitting configuration
export const bundleConfig = {
  // Vendor chunks
  vendor: [
    'react',
    'react-dom',
    'react-router-dom',
  ],
  
  // UI library chunks
  ui: [
    'lucide-react',
    '@headlessui/react',
    'framer-motion',
  ],
  
  // Utility chunks
  utils: [
    'axios',
    'date-fns',
    'lodash',
  ],
  
  // Animation chunks
  animations: [
    'gsap',
    'lottie-react',
  ]
};

// Performance monitoring for lazy components
export const trackLazyComponentLoad = (componentName) => {
  const startTime = performance.now();
  
  return () => {
    const loadTime = performance.now() - startTime;
    
    // Send to analytics if needed
    if (window.gtag) {
      window.gtag('event', 'lazy_component_load', {
        component_name: componentName,
        load_time: loadTime,
      });
    }
  };
};

// Export all lazy components
const LazyComponents = {
  // Pages
  LazyDashboardPage,
  LazyCheckLinkPage,
  LazyCommunityPage,
  LazyProfilePage,
  LazySettingsPage,

  // Features
  LazyTrendingArticles,
  LazyCommunityPreview,
  LazyLatestNews,
  LazyAnimatedStats,

  // UI Components
  LazyDataTable,
  LazyChart,
  LazyImageGallery,
  LazyVideoPlayer,

  // Auth Components
  LazyLoginForm,
  LazyRegisterForm,
  LazyForgotPasswordForm,

  // Utilities
  withLazyLoading,
  createLazyRoute,
  preloadComponents,
  preloadOnHover,
  trackLazyComponentLoad,
  bundleConfig,
};

export default LazyComponents;
