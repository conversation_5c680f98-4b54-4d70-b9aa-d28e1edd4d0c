/**
 * Lazy-loaded components for code splitting and performance optimization
 * This file centralizes all lazy imports for better bundle management
 */

import { lazy, Suspense } from 'react';
import { CodeSplittingUtils } from '../utils/performanceOptimization';

// Loading fallback components
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
  </div>
);

const LoadingCard = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 dark:bg-gray-700 rounded-xl h-48 w-full"></div>
  </div>
);

const LoadingPage = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <p className="text-gray-600 dark:text-gray-400">Loading...</p>
    </div>
  </div>
);

// Lazy-loaded page components
export const LazyHomePage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/HomePage'),
  <LoadingPage />
);

export const LazyDashboardPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/DashboardPage'),
  <LoadingPage />
);

export const LazyCheckLinkPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/CheckLinkPage'),
  <LoadingPage />
);

export const LazyCommunityPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/CommunityPage'),
  <LoadingPage />
);

export const LazyProfilePage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/ProfilePage'),
  <LoadingPage />
);

export const LazySettingsPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/SettingsPage'),
  <LoadingPage />
);

export const LazyAdminPage = CodeSplittingUtils.lazyComponent(
  () => import('../pages/AdminPage'),
  <LoadingPage />
);

// Lazy-loaded feature components
export const LazyTrendingArticles = CodeSplittingUtils.lazyComponent(
  () => import('./TrendingArticles'),
  <LoadingCard />
);

export const LazyCommunityPreview = CodeSplittingUtils.lazyComponent(
  () => import('./CommunityPreview'),
  <LoadingCard />
);

export const LazyLatestNews = CodeSplittingUtils.lazyComponent(
  () => import('./LatestNews'),
  <LoadingCard />
);

export const LazyAnimatedStats = CodeSplittingUtils.lazyComponent(
  () => import('./AnimatedStats'),
  <LoadingCard />
);

export const LazyFactCheckWidget = CodeSplittingUtils.lazyComponent(
  () => import('./FactCheckWidget'),
  <LoadingCard />
);

export const LazyChatInterface = CodeSplittingUtils.lazyComponent(
  () => import('./chat/ChatInterface'),
  <LoadingCard />
);

// Lazy-loaded UI components
export const LazyDataTable = CodeSplittingUtils.lazyComponent(
  () => import('./ui/DataTable'),
  <LoadingSpinner />
);

export const LazyChart = CodeSplittingUtils.lazyComponent(
  () => import('./ui/Chart'),
  <LoadingSpinner />
);

export const LazyImageGallery = CodeSplittingUtils.lazyComponent(
  () => import('./ui/ImageGallery'),
  <LoadingSpinner />
);

export const LazyVideoPlayer = CodeSplittingUtils.lazyComponent(
  () => import('./ui/VideoPlayer'),
  <LoadingSpinner />
);

// Lazy-loaded authentication components
export const LazyLoginForm = CodeSplittingUtils.lazyComponent(
  () => import('./auth/LoginForm'),
  <LoadingSpinner />
);

export const LazyRegisterForm = CodeSplittingUtils.lazyComponent(
  () => import('./auth/RegisterForm'),
  <LoadingSpinner />
);

export const LazyForgotPasswordForm = CodeSplittingUtils.lazyComponent(
  () => import('./auth/ForgotPasswordForm'),
  <LoadingSpinner />
);

// Higher-order component for lazy loading with error boundary
export const withLazyLoading = (LazyComponent, fallback = <LoadingSpinner />) => {
  return function LazyWrapper(props) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
};

// Route-based code splitting helper
export const createLazyRoute = (importFunc, fallback = <LoadingPage />) => {
  const LazyComponent = lazy(importFunc);
  
  return function LazyRoute(props) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
};

// Preload components for better UX
export const preloadComponents = {
  dashboard: () => import('../pages/DashboardPage'),
  community: () => import('../pages/CommunityPage'),
  checkLink: () => import('../pages/CheckLinkPage'),
  profile: () => import('../pages/ProfilePage'),
  chat: () => import('./chat/ChatInterface'),
};

// Preload critical components on user interaction
export const preloadOnHover = (componentKey) => {
  return {
    onMouseEnter: () => {
      if (preloadComponents[componentKey]) {
        preloadComponents[componentKey]();
      }
    }
  };
};

// Bundle splitting configuration
export const bundleConfig = {
  // Vendor chunks
  vendor: [
    'react',
    'react-dom',
    'react-router-dom',
  ],
  
  // UI library chunks
  ui: [
    'lucide-react',
    '@headlessui/react',
    'framer-motion',
  ],
  
  // Utility chunks
  utils: [
    'axios',
    'date-fns',
    'lodash',
  ],
  
  // Animation chunks
  animations: [
    'gsap',
    'lottie-react',
  ]
};

// Performance monitoring for lazy components
export const trackLazyComponentLoad = (componentName) => {
  const startTime = performance.now();
  
  return () => {
    const loadTime = performance.now() - startTime;
    console.log(`📊 ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
    
    // Send to analytics if needed
    if (window.gtag) {
      window.gtag('event', 'lazy_component_load', {
        component_name: componentName,
        load_time: loadTime,
      });
    }
  };
};

// Export all lazy components
export default {
  // Pages
  LazyHomePage,
  LazyDashboardPage,
  LazyCheckLinkPage,
  LazyCommunityPage,
  LazyProfilePage,
  LazySettingsPage,
  LazyAdminPage,
  
  // Features
  LazyTrendingArticles,
  LazyCommunityPreview,
  LazyLatestNews,
  LazyAnimatedStats,
  LazyFactCheckWidget,
  LazyChatInterface,
  
  // UI Components
  LazyDataTable,
  LazyChart,
  LazyImageGallery,
  LazyVideoPlayer,
  
  // Auth Components
  LazyLoginForm,
  LazyRegisterForm,
  LazyForgotPasswordForm,
  
  // Utilities
  withLazyLoading,
  createLazyRoute,
  preloadComponents,
  preloadOnHover,
  trackLazyComponentLoad,
  bundleConfig,
};
