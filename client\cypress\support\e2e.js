/**
 * Cypress E2E Support File
 * Custom commands and global configuration for end-to-end testing
 */

import './commands';
import 'cypress-axe';
import '@cypress/code-coverage/support';

// Global before hook
beforeEach(() => {
  // Inject axe-core for accessibility testing
  cy.injectAxe();
  
  // Set up viewport
  cy.viewport(1280, 720);
  
  // Clear local storage and cookies
  cy.clearLocalStorage();
  cy.clearCookies();
});

// Global after hook
afterEach(() => {
  // Take screenshot on failure
  cy.screenshot({ capture: 'runner' });
});

// Handle uncaught exceptions
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignore specific errors that are expected in development
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  
  // Let other errors fail the test
  return true;
});

// Custom error handling
Cypress.on('fail', (error, runnable) => {
  // Log additional context on failure
  cy.log('Test failed:', error.message);
  cy.log('Runnable:', runnable.title);
  
  throw error;
});
