/**
 * User-related React Query hooks
 * Comprehensive user data management with caching and optimistic updates
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { queryKeys, mutationKeys, invalidateQueries } from '../../store';
import { authAPI, userAPI } from '../../services/api';

// User profile query
export const useUserProfile = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.user.profile(),
    queryFn: async () => {
      const response = await userAPI.getProfile();
      return response.data;
    },
    enabled: !!localStorage.getItem('token'), // Only fetch if authenticated
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

// User settings query
export const useUserSettings = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.user.settings(),
    queryFn: async () => {
      const response = await userAPI.getSettings();
      return response.data;
    },
    enabled: !!localStorage.getItem('token'),
    staleTime: 15 * 60 * 1000, // 15 minutes
    ...options,
  });
};

// User history query with pagination
export const useUserHistory = (page = 1, options = {}) => {
  return useQuery({
    queryKey: queryKeys.user.history(page),
    queryFn: async () => {
      const response = await userAPI.getHistory(page);
      return response.data;
    },
    enabled: !!localStorage.getItem('token'),
    keepPreviousData: true, // Keep previous page data while loading new page
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// User favorites query
export const useUserFavorites = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.user.favorites(),
    queryFn: async () => {
      const response = await userAPI.getFavorites();
      return response.data;
    },
    enabled: !!localStorage.getItem('token'),
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

// User achievements query
export const useUserAchievements = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.user.achievements(),
    queryFn: async () => {
      const response = await userAPI.getAchievements();
      return response.data;
    },
    enabled: !!localStorage.getItem('token'),
    staleTime: 30 * 60 * 1000, // 30 minutes (achievements don't change often)
    ...options,
  });
};

// Login mutation
export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.user.login,
    mutationFn: async ({ email, password }) => {
      const response = await authAPI.login(email, password);
      return response.data;
    },
    onSuccess: (data) => {
      // Store authentication data
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Invalidate and refetch user queries
      invalidateQueries.user(queryClient);
      
      toast.success('Login successful!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Login failed';
      toast.error(message);
    },
  });
};

// Logout mutation
export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.user.logout,
    mutationFn: async () => {
      await authAPI.logout();
    },
    onSuccess: () => {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Clear all cached data
      queryClient.clear();
      
      toast.success('Logged out successfully');
    },
    onError: (error) => {
      // Still clear local data even if API call fails
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      queryClient.clear();
    },
  });
};

// Register mutation
export const useRegister = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.user.register,
    mutationFn: async ({ email, password, displayName }) => {
      const response = await authAPI.register(email, password, displayName);
      return response.data;
    },
    onSuccess: (data) => {
      // Store authentication data
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Prefetch user data
      queryClient.prefetchQuery({
        queryKey: queryKeys.user.profile(),
      });
      
      toast.success('Registration successful!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
    },
  });
};

// Update profile mutation
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.user.updateProfile,
    mutationFn: async (profileData) => {
      const response = await userAPI.updateProfile(profileData);
      return response.data;
    },
    onMutate: async (newProfileData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.user.profile() });
      
      // Snapshot previous value
      const previousProfile = queryClient.getQueryData(queryKeys.user.profile());
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.user.profile(), (old) => ({
        ...old,
        ...newProfileData,
      }));
      
      return { previousProfile };
    },
    onError: (error, newProfileData, context) => {
      // Rollback on error
      queryClient.setQueryData(queryKeys.user.profile(), context.previousProfile);
      
      const message = error.response?.data?.message || 'Failed to update profile';
      toast.error(message);
    },
    onSuccess: () => {
      toast.success('Profile updated successfully!');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
  });
};

// Update settings mutation
export const useUpdateSettings = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.user.updateSettings,
    mutationFn: async (settingsData) => {
      const response = await userAPI.updateSettings(settingsData);
      return response.data;
    },
    onMutate: async (newSettings) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.user.settings() });
      
      const previousSettings = queryClient.getQueryData(queryKeys.user.settings());
      
      queryClient.setQueryData(queryKeys.user.settings(), (old) => ({
        ...old,
        ...newSettings,
      }));
      
      return { previousSettings };
    },
    onError: (error, newSettings, context) => {
      queryClient.setQueryData(queryKeys.user.settings(), context.previousSettings);
      
      const message = error.response?.data?.message || 'Failed to update settings';
      toast.error(message);
    },
    onSuccess: () => {
      toast.success('Settings updated successfully!');
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.settings() });
    },
  });
};

// Change password mutation
export const useChangePassword = () => {
  return useMutation({
    mutationKey: mutationKeys.user.changePassword,
    mutationFn: async ({ currentPassword, newPassword }) => {
      const response = await userAPI.changePassword(currentPassword, newPassword);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Password changed successfully!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to change password';
      toast.error(message);
    },
  });
};

// Add to favorites mutation
export const useAddToFavorites = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (itemId) => {
      const response = await userAPI.addToFavorites(itemId);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate favorites query
      queryClient.invalidateQueries({ queryKey: queryKeys.user.favorites() });
      toast.success('Added to favorites!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to add to favorites';
      toast.error(message);
    },
  });
};

// Remove from favorites mutation
export const useRemoveFromFavorites = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (itemId) => {
      const response = await userAPI.removeFromFavorites(itemId);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.favorites() });
      toast.success('Removed from favorites!');
    },
    onError: (error) => {
      const message = error.response?.data?.message || 'Failed to remove from favorites';
      toast.error(message);
    },
  });
};

// Custom hook for user authentication state
export const useAuthState = () => {
  const { data: profile, isLoading, error } = useUserProfile({
    retry: false, // Don't retry if not authenticated
  });
  
  const isAuthenticated = !!localStorage.getItem('token') && !!profile;
  const user = profile || JSON.parse(localStorage.getItem('user') || 'null');
  
  return {
    user,
    isAuthenticated,
    isLoading,
    error,
  };
};

// Custom hook for user permissions
export const useUserPermissions = () => {
  const { user } = useAuthState();
  
  const permissions = {
    canCreatePost: !!user?.emailVerified,
    canVote: !!user,
    canComment: !!user?.emailVerified,
    canModerate: user?.role === 'moderator' || user?.role === 'admin',
    canAdmin: user?.role === 'admin',
    canDeleteOwnContent: !!user,
    canEditOwnContent: !!user,
  };
  
  return permissions;
};

export default {
  useUserProfile,
  useUserSettings,
  useUserHistory,
  useUserFavorites,
  useUserAchievements,
  useLogin,
  useLogout,
  useRegister,
  useUpdateProfile,
  useUpdateSettings,
  useChangePassword,
  useAddToFavorites,
  useRemoveFromFavorites,
  useAuthState,
  useUserPermissions,
};
