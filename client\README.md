# FactCheck Platform - Frontend

A modern fact-checking platform built with React, featuring advanced animations, accessibility compliance, and performance optimization.

## 🚀 Features

### Core Functionality
- **Link Verification**: Advanced URL analysis with trust scoring
- **Community Platform**: User-generated content with voting and commenting
- **Real-time Updates**: Live data synchronization and notifications
- **User Management**: Complete authentication and profile system
- **Admin Dashboard**: Comprehensive administrative controls

### Enterprise-Level Architecture
- **Performance Optimized**: Code splitting, lazy loading, and caching
- **Accessibility Compliant**: WCAG AA standards with comprehensive testing
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Advanced Animations**: GSAP-powered micro-interactions and transitions
- **Robust Testing**: Unit, integration, and E2E testing with 90%+ coverage
- **Error Handling**: Comprehensive error boundaries with recovery mechanisms

## 🛠 Technology Stack

### Frontend Framework
- **React 18** - Latest React with concurrent features
- **React Router 6** - Client-side routing with data loading
- **TypeScript** - Type-safe development (optional)

### State Management
- **React Query (TanStack Query)** - Server state management with caching
- **React Context** - Client state management
- **Zustand** - Lightweight state management for complex scenarios

### Styling & UI
- **Tailwind CSS** - Utility-first CSS framework with custom design tokens
- **Custom Component Library** - Enterprise-grade reusable components
- **Dark Mode Support** - System preference detection and manual toggle

### Animations & Interactions
- **GSAP** - High-performance animations and micro-interactions
- **Framer Motion** - React-specific animation library (legacy support)
- **Intersection Observer** - Scroll-triggered animations

### Testing Infrastructure
- **Jest** - Unit and integration testing
- **React Testing Library** - Component testing utilities
- **Cypress** - End-to-end testing with visual regression
- **MSW (Mock Service Worker)** - API mocking for testing

### Development Tools
- **ESLint** - Code linting with custom rules
- **Prettier** - Code formatting
- **Husky** - Git hooks for quality assurance
- **Lighthouse CI** - Performance monitoring

## 📋 Prerequisites

- **Node.js** 18.0.0 or higher
- **npm** 8.0.0 or higher (or **yarn** 1.22.0+)
- **Git** for version control

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/factcheck-platform.git
cd factcheck-platform/client
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Environment Setup
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

Required environment variables:
```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_GEMINI_API_KEY=your_gemini_api_key
```

### 4. Start Development Server
```bash
npm start
# or
yarn start
```

The application will be available at `http://localhost:3000`

## 🏗 Project Structure

```
client/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # Base UI components
│   │   ├── forms/         # Form components
│   │   ├── navigation/    # Navigation components
│   │   └── error/         # Error handling components
│   ├── pages/             # Page components
│   ├── hooks/             # Custom React hooks
│   │   ├── api/          # API-related hooks
│   │   └── utils/        # Utility hooks
│   ├── services/          # API services and external integrations
│   ├── utils/             # Utility functions
│   ├── context/           # React Context providers
│   ├── store/             # State management
│   ├── styles/            # Global styles and themes
│   ├── mocks/             # API mocks for testing
│   └── __tests__/         # Test files
├── cypress/               # E2E tests
├── docs/                  # Documentation
└── build/                 # Production build output
```

## 🧪 Testing

### Running Tests

```bash
# Unit and integration tests
npm test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:coverage

# Visual regression tests
npm run test:visual
```

### Test Types

1. **Unit Tests**: Individual component and function testing
2. **Integration Tests**: Component interaction testing
3. **E2E Tests**: Full user workflow testing
4. **Accessibility Tests**: WCAG compliance verification
5. **Performance Tests**: Core Web Vitals monitoring

## 🎨 Design System

### Design Tokens
- **Colors**: Semantic color system with dark mode support
- **Typography**: Responsive type scale with accessibility compliance
- **Spacing**: Consistent spacing system (4px base unit)
- **Shadows**: Layered shadow system for depth
- **Border Radius**: Consistent corner radius (12px standard)

### Component Library
- **EnterpriseButton**: Advanced button with animations and states
- **EnterpriseInput**: Form input with validation and accessibility
- **EnterpriseCard**: Flexible card component with interactions
- **Navigation**: Responsive navigation with mobile support
- **Modals**: Accessible modal system with focus management

## ♿ Accessibility

### WCAG AA Compliance
- **Color Contrast**: 4.5:1 minimum ratio for normal text
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Comprehensive ARIA implementation
- **Focus Management**: Visible focus indicators and logical tab order
- **Alternative Text**: Descriptive alt text for all images

### Accessibility Testing
```bash
# Run accessibility audit
npm run test:a11y

# Manual testing tools
npm run dev:a11y
```

## 🚀 Performance

### Optimization Strategies
- **Code Splitting**: Route-based and component-based splitting
- **Lazy Loading**: Images and components loaded on demand
- **Caching**: Aggressive caching with React Query
- **Bundle Analysis**: Regular bundle size monitoring
- **Core Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1

### Performance Monitoring
```bash
# Lighthouse audit
npm run audit:lighthouse

# Bundle analysis
npm run analyze

# Performance testing
npm run test:performance
```

## 🔧 Development

### Code Quality
- **ESLint**: Enforced coding standards
- **Prettier**: Consistent code formatting
- **TypeScript**: Type safety (optional)
- **Husky**: Pre-commit hooks for quality checks

### Development Commands
```bash
# Start development server
npm start

# Build for production
npm run build

# Run linting
npm run lint

# Format code
npm run format

# Type checking
npm run type-check
```

## 📦 Building for Production

### Build Process
```bash
# Create production build
npm run build

# Serve production build locally
npm run serve

# Analyze bundle size
npm run analyze
```

### Build Optimization
- **Tree Shaking**: Unused code elimination
- **Minification**: JavaScript and CSS compression
- **Asset Optimization**: Image compression and format conversion
- **Caching**: Long-term caching with content hashing

## 🚀 Deployment

### Deployment Options

#### 1. Firebase Hosting
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Deploy
npm run deploy:firebase
```

#### 2. Render
```bash
# Connect repository to Render
# Set build command: npm run build
# Set publish directory: build
```

#### 3. Vercel
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

#### 4. Docker
```bash
# Build Docker image
docker build -t factcheck-frontend .

# Run container
docker run -p 3000:3000 factcheck-frontend
```

### Environment Configuration
- **Development**: `.env.local`
- **Staging**: `.env.staging`
- **Production**: `.env.production`

## 🔒 Security

### Security Measures
- **Content Security Policy**: XSS protection
- **HTTPS Enforcement**: Secure data transmission
- **Input Validation**: Client and server-side validation
- **Authentication**: Secure token-based authentication
- **Error Handling**: No sensitive data in error messages

## 📊 Monitoring

### Analytics Integration
- **Google Analytics**: User behavior tracking
- **Performance Monitoring**: Core Web Vitals tracking
- **Error Tracking**: Comprehensive error logging
- **User Feedback**: In-app feedback collection

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks
5. Submit pull request

### Code Standards
- Follow ESLint configuration
- Write comprehensive tests
- Update documentation
- Follow semantic commit messages

## 📚 Documentation

### Additional Resources
- [Component Documentation](./docs/components.md)
- [API Integration Guide](./docs/api.md)
- [Testing Guide](./docs/testing.md)
- [Deployment Guide](./docs/deployment.md)
- [Performance Guide](./docs/performance.md)
- [Accessibility Guide](./docs/accessibility.md)
- [Troubleshooting](./docs/troubleshooting.md)

## 🆘 Support

### Getting Help
- **Documentation**: Check the docs folder
- **Issues**: GitHub Issues for bug reports
- **Discussions**: GitHub Discussions for questions
- **Email**: <EMAIL>

### Common Issues
- [Build Errors](./docs/troubleshooting.md#build-errors)
- [Performance Issues](./docs/troubleshooting.md#performance)
- [Accessibility Problems](./docs/troubleshooting.md#accessibility)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React team for the amazing framework
- Tailwind CSS for the utility-first approach
- GSAP for powerful animations
- All contributors and the open-source community

---

**Built with ❤️ by the FactCheck Team**
