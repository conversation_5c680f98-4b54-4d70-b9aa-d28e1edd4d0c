/**
 * Enterprise Testing Utilities
 * Comprehensive testing helpers and custom render functions
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '../context/ThemeContext';
import { AuthProvider } from '../context/AuthContext';

// Create a custom render function that includes providers
export const renderWithProviders = (
  ui,
  {
    initialEntries = ['/'],
    user = null,
    theme = 'light',
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    }),
    ...renderOptions
  } = {}
) => {
  // Mock auth context value
  const mockAuthValue = {
    user,
    loading: false,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
    resetPassword: jest.fn(),
    updateProfile: jest.fn(),
  };

  // Mock theme context value
  const mockThemeValue = {
    isDarkMode: theme === 'dark',
    toggleTheme: jest.fn(),
    theme,
  };

  function Wrapper({ children }) {
    return (
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <AuthProvider value={mockAuthValue}>
            <ThemeProvider value={mockThemeValue}>
              {children}
            </ThemeProvider>
          </AuthProvider>
        </QueryClientProvider>
      </BrowserRouter>
    );
  }

  return {
    user: userEvent.setup(),
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    mockAuthValue,
    mockThemeValue,
    queryClient,
  };
};

// Accessibility testing utilities
export const accessibilityUtils = {
  // Check if element has proper ARIA attributes
  hasAriaLabel: (element) => {
    return element.getAttribute('aria-label') !== null ||
           element.getAttribute('aria-labelledby') !== null;
  },

  // Check if element is keyboard accessible
  isKeyboardAccessible: (element) => {
    const tabIndex = element.getAttribute('tabindex');
    const role = element.getAttribute('role');
    const tagName = element.tagName.toLowerCase();
    
    return (
      tabIndex !== '-1' &&
      (tabIndex !== null || 
       ['button', 'a', 'input', 'select', 'textarea'].includes(tagName) ||
       role === 'button' || role === 'link')
    );
  },

  // Check color contrast (simplified)
  hasGoodContrast: (element) => {
    const style = window.getComputedStyle(element);
    const color = style.color;
    const backgroundColor = style.backgroundColor;
    
    // This is a simplified check - in practice, use a proper contrast library
    return color !== backgroundColor && color !== 'rgba(0, 0, 0, 0)';
  },

  // Test keyboard navigation
  testKeyboardNavigation: async (container) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const results = [];
    
    for (let i = 0; i < focusableElements.length; i++) {
      const element = focusableElements[i];
      
      // Test focus
      element.focus();
      expect(document.activeElement).toBe(element);
      
      // Test keyboard activation
      if (element.tagName === 'BUTTON' || element.getAttribute('role') === 'button') {
        const clickHandler = jest.fn();
        element.addEventListener('click', clickHandler);
        
        fireEvent.keyDown(element, { key: 'Enter' });
        expect(clickHandler).toHaveBeenCalled();
        
        fireEvent.keyDown(element, { key: ' ' });
        expect(clickHandler).toHaveBeenCalledTimes(2);
      }
      
      results.push({
        element,
        hasAriaLabel: accessibilityUtils.hasAriaLabel(element),
        isKeyboardAccessible: accessibilityUtils.isKeyboardAccessible(element),
        hasGoodContrast: accessibilityUtils.hasGoodContrast(element),
      });
    }
    
    return results;
  },
};

// Performance testing utilities
export const performanceUtils = {
  // Measure component render time
  measureRenderTime: async (renderFunction) => {
    const start = performance.now();
    const result = await renderFunction();
    const end = performance.now();
    
    return {
      renderTime: end - start,
      result,
    };
  },

  // Test for memory leaks
  testMemoryLeaks: (component, iterations = 100) => {
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    
    for (let i = 0; i < iterations; i++) {
      const { unmount } = render(component);
      unmount();
    }
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    const finalMemory = performance.memory?.usedJSHeapSize || 0;
    const memoryIncrease = finalMemory - initialMemory;
    
    return {
      initialMemory,
      finalMemory,
      memoryIncrease,
      iterations,
      memoryPerIteration: memoryIncrease / iterations,
    };
  },

  // Test animation performance
  testAnimationPerformance: (animationFunction, duration = 1000) => {
    const frames = [];
    let startTime = performance.now();
    
    const measureFrame = () => {
      const currentTime = performance.now();
      frames.push(currentTime - startTime);
      startTime = currentTime;
      
      if (currentTime < startTime + duration) {
        requestAnimationFrame(measureFrame);
      }
    };
    
    animationFunction();
    requestAnimationFrame(measureFrame);
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const avgFrameTime = frames.reduce((a, b) => a + b, 0) / frames.length;
        const fps = 1000 / avgFrameTime;
        
        resolve({
          frames: frames.length,
          avgFrameTime,
          fps,
          droppedFrames: frames.filter(time => time > 16.67).length, // 60fps threshold
        });
      }, duration + 100);
    });
  },
};

// Form testing utilities
export const formUtils = {
  // Fill form with data
  fillForm: async (formData) => {
    const user = userEvent.setup();
    
    for (const [fieldName, value] of Object.entries(formData)) {
      const field = screen.getByLabelText(new RegExp(fieldName, 'i')) ||
                   screen.getByPlaceholderText(new RegExp(fieldName, 'i')) ||
                   screen.getByDisplayValue('') ||
                   screen.getByRole('textbox', { name: new RegExp(fieldName, 'i') });
      
      if (field.type === 'checkbox' || field.type === 'radio') {
        if (value) {
          await user.click(field);
        }
      } else if (field.tagName === 'SELECT') {
        await user.selectOptions(field, value);
      } else {
        await user.clear(field);
        await user.type(field, value);
      }
    }
  },

  // Submit form and wait for response
  submitForm: async (submitButtonText = /submit/i) => {
    const user = userEvent.setup();
    const submitButton = screen.getByRole('button', { name: submitButtonText });
    
    await user.click(submitButton);
    
    // Wait for form submission to complete
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });
  },

  // Validate form errors
  expectFormErrors: (expectedErrors) => {
    expectedErrors.forEach(error => {
      expect(screen.getByText(error)).toBeInTheDocument();
    });
  },
};

// API testing utilities
export const apiUtils = {
  // Mock successful API response
  mockApiSuccess: (data) => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(data),
      })
    );
  },

  // Mock API error
  mockApiError: (status = 500, message = 'Internal Server Error') => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        status,
        json: () => Promise.resolve({ error: message }),
      })
    );
  },

  // Mock API loading state
  mockApiLoading: (delay = 1000) => {
    global.fetch = jest.fn(() =>
      new Promise(resolve => {
        setTimeout(() => {
          resolve({
            ok: true,
            status: 200,
            json: () => Promise.resolve({}),
          });
        }, delay);
      })
    );
  },

  // Wait for API call to complete
  waitForApiCall: async (apiFunction, timeout = 5000) => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const result = await apiFunction();
        return result;
      } catch (error) {
        if (Date.now() - startTime >= timeout) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    throw new Error('API call timeout');
  },
};

// Component testing utilities
export const componentUtils = {
  // Test component props
  testProps: (Component, propTests) => {
    propTests.forEach(({ props, expectedBehavior }) => {
      const { container } = render(<Component {...props} />);
      expectedBehavior(container);
    });
  },

  // Test component states
  testStates: async (Component, stateTests) => {
    for (const { stateName, trigger, expectedResult } of stateTests) {
      const { container } = render(<Component />);
      
      if (trigger) {
        await trigger(container);
      }
      
      await waitFor(() => {
        expectedResult(container);
      });
    }
  },

  // Test component events
  testEvents: async (Component, eventTests) => {
    const user = userEvent.setup();
    
    for (const { eventName, trigger, expectedCallback } of eventTests) {
      const mockCallback = jest.fn();
      const props = { [eventName]: mockCallback };
      
      const { container } = render(<Component {...props} />);
      
      await trigger(container, user);
      
      expect(mockCallback).toHaveBeenCalled();
      
      if (expectedCallback) {
        expectedCallback(mockCallback);
      }
    }
  },
};

// Export commonly used testing functions
export {
  render,
  screen,
  waitFor,
  fireEvent,
  userEvent,
};

// Export custom matchers
export const customMatchers = {
  toBeAccessible: accessibilityUtils.hasAriaLabel,
  toHaveGoodContrast: accessibilityUtils.hasGoodContrast,
  toBeKeyboardAccessible: accessibilityUtils.isKeyboardAccessible,
};

// Default export with all utilities
export default {
  renderWithProviders,
  accessibilityUtils,
  performanceUtils,
  formUtils,
  apiUtils,
  componentUtils,
  customMatchers,
};
