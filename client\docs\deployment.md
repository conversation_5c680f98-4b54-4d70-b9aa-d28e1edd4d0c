# Deployment Guide

This guide covers comprehensive deployment strategies for the FactCheck Platform frontend across different environments and platforms.

## Table of Contents

1. [Pre-deployment Checklist](#pre-deployment-checklist)
2. [Environment Configuration](#environment-configuration)
3. [Build Process](#build-process)
4. [Deployment Platforms](#deployment-platforms)
5. [CI/CD Pipeline](#cicd-pipeline)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Pre-deployment Checklist

### Code Quality
- [ ] All tests passing (unit, integration, E2E)
- [ ] Code coverage above 80%
- [ ] ESLint and Prettier checks passing
- [ ] No console errors or warnings
- [ ] Accessibility audit passing (WCAG AA)
- [ ] Performance audit passing (Lighthouse score > 90)

### Security
- [ ] Environment variables properly configured
- [ ] No sensitive data in client-side code
- [ ] Content Security Policy configured
- [ ] HTTPS enforced
- [ ] API endpoints secured

### Performance
- [ ] Bundle size optimized (< 500KB gzipped)
- [ ] Images optimized and compressed
- [ ] Lazy loading implemented
- [ ] Caching strategies configured
- [ ] Core Web Vitals meeting targets

## Environment Configuration

### Environment Files

Create environment files for each deployment stage:

#### Development (`.env.local`)
```env
REACT_APP_ENV=development
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_FIREBASE_API_KEY=dev_firebase_key
REACT_APP_FIREBASE_AUTH_DOMAIN=dev-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=dev-project-id
REACT_APP_GEMINI_API_KEY=dev_gemini_key
REACT_APP_ANALYTICS_ID=
REACT_APP_SENTRY_DSN=
```

#### Staging (`.env.staging`)
```env
REACT_APP_ENV=staging
REACT_APP_API_URL=https://api-staging.factcheck.com/api
REACT_APP_FIREBASE_API_KEY=staging_firebase_key
REACT_APP_FIREBASE_AUTH_DOMAIN=staging-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=staging-project-id
REACT_APP_GEMINI_API_KEY=staging_gemini_key
REACT_APP_ANALYTICS_ID=GA_STAGING_ID
REACT_APP_SENTRY_DSN=staging_sentry_dsn
```

#### Production (`.env.production`)
```env
REACT_APP_ENV=production
REACT_APP_API_URL=https://api.factcheck.com/api
REACT_APP_FIREBASE_API_KEY=prod_firebase_key
REACT_APP_FIREBASE_AUTH_DOMAIN=factcheck-prod.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=factcheck-prod
REACT_APP_GEMINI_API_KEY=prod_gemini_key
REACT_APP_ANALYTICS_ID=GA_PRODUCTION_ID
REACT_APP_SENTRY_DSN=production_sentry_dsn
```

### Environment Variable Security

**Never commit sensitive environment variables to version control.**

Use platform-specific secret management:
- **Vercel**: Environment Variables in dashboard
- **Netlify**: Site settings > Environment variables
- **Firebase**: Firebase CLI with `.env` files
- **Render**: Environment tab in service settings

## Build Process

### Production Build

```bash
# Install dependencies
npm ci

# Run tests
npm run test:ci

# Build for production
npm run build

# Verify build
npm run serve
```

### Build Optimization

#### 1. Bundle Analysis
```bash
# Analyze bundle size
npm run analyze

# Check for duplicate dependencies
npx webpack-bundle-analyzer build/static/js/*.js
```

#### 2. Performance Optimization
```bash
# Lighthouse audit
npm run audit:lighthouse

# Performance testing
npm run test:performance
```

#### 3. Security Scan
```bash
# Dependency vulnerability check
npm audit

# Security scan
npm run security:scan
```

## Deployment Platforms

### 1. Vercel (Recommended)

#### Setup
```bash
# Install Vercel CLI
npm install -g vercel

# Login
vercel login

# Deploy
vercel --prod
```

#### Configuration (`vercel.json`)
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "build"
      }
    }
  ],
  "routes": [
    {
      "src": "/static/(.*)",
      "headers": {
        "cache-control": "public, max-age=31536000, immutable"
      }
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### 2. Netlify

#### Setup
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login
netlify login

# Deploy
netlify deploy --prod --dir=build
```

#### Configuration (`netlify.toml`)
```toml
[build]
  publish = "build"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### 3. Firebase Hosting

#### Setup
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login
firebase login

# Initialize
firebase init hosting

# Deploy
firebase deploy --only hosting
```

#### Configuration (`firebase.json`)
```json
{
  "hosting": {
    "public": "build",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/static/**",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "public, max-age=31536000, immutable"
          }
        ]
      }
    ]
  }
}
```

### 4. Render

#### Setup
1. Connect GitHub repository
2. Configure build settings:
   - **Build Command**: `npm run build`
   - **Publish Directory**: `build`
   - **Node Version**: `18`

#### Configuration (`render.yaml`)
```yaml
services:
  - type: web
    name: factcheck-frontend
    env: node
    buildCommand: npm run build
    staticPublishPath: ./build
    envVars:
      - key: NODE_VERSION
        value: 18
    headers:
      - path: /*
        name: X-Frame-Options
        value: DENY
      - path: /*
        name: X-Content-Type-Options
        value: nosniff
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
```

### 5. Docker Deployment

#### Dockerfile
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy build files
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Security headers
RUN echo 'add_header X-Frame-Options "DENY" always;' > /etc/nginx/conf.d/security.conf && \
    echo 'add_header X-Content-Type-Options "nosniff" always;' >> /etc/nginx/conf.d/security.conf && \
    echo 'add_header X-XSS-Protection "1; mode=block" always;' >> /etc/nginx/conf.d/security.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        include /etc/nginx/conf.d/security.conf;

        # Cache static assets
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
```

#### Build and Deploy
```bash
# Build Docker image
docker build -t factcheck-frontend .

# Run locally
docker run -p 3000:80 factcheck-frontend

# Deploy to registry
docker tag factcheck-frontend your-registry/factcheck-frontend:latest
docker push your-registry/factcheck-frontend:latest
```

## CI/CD Pipeline

### GitHub Actions

#### `.github/workflows/deploy.yml`
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test:ci
      
      - name: Run E2E tests
        run: npm run test:e2e:ci
      
      - name: Accessibility audit
        run: npm run test:a11y
      
      - name: Performance audit
        run: npm run audit:lighthouse

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          REACT_APP_API_URL: ${{ secrets.REACT_APP_API_URL }}
          REACT_APP_FIREBASE_API_KEY: ${{ secrets.REACT_APP_FIREBASE_API_KEY }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: build/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: build/
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

## Monitoring and Maintenance

### Performance Monitoring

#### 1. Core Web Vitals
```javascript
// web-vitals.js
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  // Send to your analytics service
  gtag('event', metric.name, {
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    event_category: 'Web Vitals',
    event_label: metric.id,
    non_interaction: true,
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

#### 2. Error Tracking
```javascript
// error-tracking.js
import * as Sentry from '@sentry/react';

Sentry.init({
  dsn: process.env.REACT_APP_SENTRY_DSN,
  environment: process.env.REACT_APP_ENV,
  tracesSampleRate: 1.0,
});
```

### Health Checks

#### Deployment Health Check
```bash
#!/bin/bash
# health-check.sh

URL="https://your-domain.com"
EXPECTED_STATUS=200

# Check if site is accessible
STATUS=$(curl -s -o /dev/null -w "%{http_code}" $URL)

if [ $STATUS -eq $EXPECTED_STATUS ]; then
  echo "✅ Site is healthy (Status: $STATUS)"
  exit 0
else
  echo "❌ Site is unhealthy (Status: $STATUS)"
  exit 1
fi
```

## Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for dependency conflicts
npm ls
```

#### 2. Environment Variable Issues
```bash
# Verify environment variables are loaded
npm run build -- --verbose

# Check for missing variables
grep -r "process.env" src/
```

#### 3. Performance Issues
```bash
# Analyze bundle size
npm run analyze

# Check for memory leaks
npm run test:memory
```

#### 4. Deployment Failures
```bash
# Check deployment logs
vercel logs your-deployment-url

# Verify build output
ls -la build/
```

### Rollback Strategy

#### Quick Rollback
```bash
# Vercel
vercel rollback

# Netlify
netlify rollback

# Firebase
firebase hosting:clone source-site-id:source-version-id target-site-id
```

#### Manual Rollback
1. Identify last known good commit
2. Create rollback branch
3. Deploy rollback branch
4. Verify functionality
5. Investigate and fix issues

---

For additional support, refer to the [troubleshooting guide](./troubleshooting.md) or contact the development team.
