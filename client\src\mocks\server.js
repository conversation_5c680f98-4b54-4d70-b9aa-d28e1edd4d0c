/**
 * Mock Service Worker Server Setup
 * Provides API mocking for testing and development
 */

import { setupServer } from 'msw/node';
import { handlers } from './handlers';

// Setup server with request handlers
export const server = setupServer(...handlers);

// Enable API mocking in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 MSW server configured for development');
}
