/**
 * Custom Cypress Commands
 * Enterprise-level testing commands for common operations
 */

// Authentication commands
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password123') => {
  cy.visit('/login');
  
  cy.get('[data-testid="email-input"]').type(email);
  cy.get('[data-testid="password-input"]').type(password);
  cy.get('[data-testid="login-button"]').click();
  
  // Wait for redirect to dashboard
  cy.url().should('include', '/dashboard');
  cy.get('[data-testid="user-menu"]').should('be.visible');
});

Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.get('[data-testid="logout-button"]').click();
  
  // Wait for redirect to home
  cy.url().should('eq', Cypress.config().baseUrl + '/');
});

Cypress.Commands.add('register', (userData) => {
  const defaultUser = {
    email: '<EMAIL>',
    password: 'password123',
    displayName: 'New User',
  };
  
  const user = { ...defaultUser, ...userData };
  
  cy.visit('/register');
  
  cy.get('[data-testid="email-input"]').type(user.email);
  cy.get('[data-testid="password-input"]').type(user.password);
  cy.get('[data-testid="display-name-input"]').type(user.displayName);
  cy.get('[data-testid="register-button"]').click();
  
  // Wait for success message or redirect
  cy.get('[data-testid="registration-success"]').should('be.visible');
});

// Navigation commands
Cypress.Commands.add('navigateTo', (page) => {
  const routes = {
    home: '/',
    dashboard: '/dashboard',
    community: '/community',
    checkLink: '/check',
    profile: '/profile',
    settings: '/settings',
  };
  
  const route = routes[page] || page;
  cy.visit(route);
  
  // Wait for page to load
  cy.get('[data-testid="page-content"]').should('be.visible');
});

// Form commands
Cypress.Commands.add('fillForm', (formData) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[data-testid="${field}-input"]`).clear().type(value);
  });
});

Cypress.Commands.add('submitForm', (buttonTestId = 'submit-button') => {
  cy.get(`[data-testid="${buttonTestId}"]`).click();
});

// API commands
Cypress.Commands.add('mockApiResponse', (method, url, response, statusCode = 200) => {
  cy.intercept(method, url, {
    statusCode,
    body: response,
  }).as('apiCall');
});

Cypress.Commands.add('waitForApi', (alias = 'apiCall') => {
  cy.wait(`@${alias}`);
});

// Accessibility commands
Cypress.Commands.add('checkA11y', (context = null, options = {}) => {
  const defaultOptions = {
    rules: {
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'aria-labels': { enabled: true },
    },
    tags: ['wcag2a', 'wcag2aa'],
    ...options,
  };
  
  cy.checkAxe(context, defaultOptions);
});

Cypress.Commands.add('testKeyboardNavigation', (selector) => {
  cy.get(selector).first().focus();
  
  // Test Tab navigation
  cy.get(selector).each(($el, index) => {
    if (index > 0) {
      cy.focused().tab();
    }
    cy.focused().should('have.attr', 'data-testid', $el.attr('data-testid'));
  });
});

Cypress.Commands.add('testScreenReader', (selector, expectedText) => {
  cy.get(selector).should('have.attr', 'aria-label', expectedText)
    .or('have.attr', 'aria-labelledby')
    .or('contain.text', expectedText);
});

// Performance commands
Cypress.Commands.add('measurePerformance', (pageName) => {
  cy.window().then((win) => {
    // Measure page load performance
    const navigation = win.performance.getEntriesByType('navigation')[0];
    const paintEntries = win.performance.getEntriesByType('paint');
    
    const metrics = {
      page: pageName,
      loadTime: navigation.loadEventEnd - navigation.loadEventStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime,
      firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime,
    };
    
    cy.log('Performance Metrics:', metrics);
    
    // Assert performance thresholds
    expect(metrics.loadTime).to.be.lessThan(3000); // 3 seconds
    expect(metrics.domContentLoaded).to.be.lessThan(2000); // 2 seconds
  });
});

// Visual testing commands
Cypress.Commands.add('compareSnapshot', (name, options = {}) => {
  const defaultOptions = {
    threshold: 0.1,
    thresholdType: 'percent',
    ...options,
  };
  
  cy.matchImageSnapshot(name, defaultOptions);
});

// Data commands
Cypress.Commands.add('seedTestData', (dataType) => {
  const seedData = {
    users: [
      { id: '1', email: '<EMAIL>', displayName: 'User One' },
      { id: '2', email: '<EMAIL>', displayName: 'User Two' },
    ],
    posts: [
      { id: '1', title: 'Test Post 1', content: 'Content 1', authorId: '1' },
      { id: '2', title: 'Test Post 2', content: 'Content 2', authorId: '2' },
    ],
  };
  
  if (dataType && seedData[dataType]) {
    cy.task('seedDatabase', { type: dataType, data: seedData[dataType] });
  } else {
    cy.task('seedDatabase', seedData);
  }
});

Cypress.Commands.add('cleanTestData', () => {
  cy.task('cleanDatabase');
});

// Wait commands
Cypress.Commands.add('waitForElement', (selector, timeout = 10000) => {
  cy.get(selector, { timeout }).should('be.visible');
});

Cypress.Commands.add('waitForText', (text, timeout = 10000) => {
  cy.contains(text, { timeout }).should('be.visible');
});

// Animation commands
Cypress.Commands.add('waitForAnimation', (selector, duration = 1000) => {
  cy.get(selector).should('be.visible');
  cy.wait(duration); // Wait for animation to complete
});

// Mobile testing commands
Cypress.Commands.add('setMobileViewport', () => {
  cy.viewport(375, 667); // iPhone SE dimensions
});

Cypress.Commands.add('setTabletViewport', () => {
  cy.viewport(768, 1024); // iPad dimensions
});

Cypress.Commands.add('setDesktopViewport', () => {
  cy.viewport(1280, 720); // Desktop dimensions
});

// Error handling commands
Cypress.Commands.add('expectError', (errorMessage) => {
  cy.get('[data-testid="error-message"]').should('contain.text', errorMessage);
});

Cypress.Commands.add('expectSuccess', (successMessage) => {
  cy.get('[data-testid="success-message"]').should('contain.text', successMessage);
});

// Loading state commands
Cypress.Commands.add('expectLoading', () => {
  cy.get('[data-testid="loading-spinner"]').should('be.visible');
});

Cypress.Commands.add('waitForLoading', () => {
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
});

// Custom assertions
Cypress.Commands.add('shouldBeAccessible', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject).should('have.attr', 'tabindex').and('not.equal', '-1');
  cy.wrap(subject).should('satisfy', ($el) => {
    return $el.attr('aria-label') || $el.attr('aria-labelledby') || $el.text().trim().length > 0;
  });
});

Cypress.Commands.add('shouldHaveGoodContrast', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject).should('satisfy', ($el) => {
    const style = window.getComputedStyle($el[0]);
    const color = style.color;
    const backgroundColor = style.backgroundColor;
    
    // Simplified contrast check
    return color !== backgroundColor && color !== 'rgba(0, 0, 0, 0)';
  });
});
