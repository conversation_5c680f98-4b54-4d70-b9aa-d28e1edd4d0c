/**
 * Accessibility Styles for Enterprise-Level WCAG AA Compliance
 * High contrast mode, focus indicators, and accessibility enhancements
 */

/* Skip Links */
.skip-links {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
  font-weight: 600;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 0;
}

/* Enhanced Focus Indicators */
*:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* High Contrast Mode Styles */
.high-contrast {
  /* Text and backgrounds */
  --text-primary: #000000;
  --text-secondary: #000000;
  --bg-primary: #ffffff;
  --bg-secondary: #f0f0f0;
  --border-color: #000000;
  --link-color: #0000ff;
  --link-visited: #800080;
  --button-bg: #000000;
  --button-text: #ffffff;
  --input-bg: #ffffff;
  --input-border: #000000;
}

.high-contrast * {
  color: var(--text-primary) !important;
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
}

.high-contrast a {
  color: var(--link-color) !important;
  text-decoration: underline !important;
}

.high-contrast a:visited {
  color: var(--link-visited) !important;
}

.high-contrast button,
.high-contrast .btn {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
  border: 2px solid var(--border-color) !important;
}

.high-contrast input,
.high-contrast select,
.high-contrast textarea {
  background-color: var(--input-bg) !important;
  color: var(--text-primary) !important;
  border: 2px solid var(--input-border) !important;
}

.high-contrast .card,
.high-contrast .modal,
.high-contrast .dropdown {
  background-color: var(--bg-secondary) !important;
  border: 2px solid var(--border-color) !important;
}

/* Remove shadows and gradients in high contrast */
.high-contrast * {
  box-shadow: none !important;
  background-image: none !important;
  text-shadow: none !important;
}

/* Ensure icons are visible */
.high-contrast svg,
.high-contrast img {
  filter: contrast(100%) brightness(0) !important;
}

.high-contrast .icon-white svg {
  filter: contrast(100%) brightness(1) !important;
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Media Query Support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #000000;
    --bg-primary: #ffffff;
    --bg-secondary: #f0f0f0;
    --border-color: #000000;
    --link-color: #0000ff;
    --button-bg: #000000;
    --button-text: #ffffff;
  }
}

/* Windows High Contrast Mode */
@media (-ms-high-contrast: active) {
  * {
    background-color: ButtonFace !important;
    color: ButtonText !important;
    border-color: ButtonText !important;
  }
  
  a {
    color: LinkText !important;
  }
  
  button {
    background-color: ButtonFace !important;
    color: ButtonText !important;
    border: 1px solid ButtonText !important;
  }
}

/* Font Size Scaling */
html {
  font-size: 16px; /* Base font size */
}

/* Ensure minimum touch target sizes (44px x 44px) */
button,
input[type="button"],
input[type="submit"],
input[type="reset"],
a {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Exception for small buttons with explicit class */
.btn-sm,
.button-small {
  min-height: 32px;
  min-width: 32px;
}

/* Ensure sufficient color contrast for placeholders */
::placeholder {
  color: #6b7280;
  opacity: 1;
}

.high-contrast ::placeholder {
  color: #000000 !important;
  opacity: 0.7 !important;
}

/* Focus trap for modals */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Keyboard navigation indicators */
.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation select:focus,
.keyboard-navigation textarea:focus {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.3);
}

/* Error states with sufficient contrast */
.error,
.invalid {
  border-color: #dc2626 !important;
  color: #dc2626 !important;
}

.error:focus,
.invalid:focus {
  outline-color: #dc2626 !important;
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2) !important;
}

/* Success states */
.success,
.valid {
  border-color: #16a34a !important;
  color: #16a34a !important;
}

/* Warning states */
.warning {
  border-color: #d97706 !important;
  color: #d97706 !important;
}

/* Ensure text remains readable at all zoom levels */
@media (min-resolution: 192dpi) {
  body {
    font-size: 1.1em;
  }
}

/* Print accessibility */
@media print {
  .skip-links,
  .sr-only {
    display: none !important;
  }
  
  a::after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: #666;
  }
  
  a[href^="#"]::after,
  a[href^="javascript:"]::after {
    content: "";
  }
}

/* Dark mode accessibility enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --focus-color: #60a5fa;
  }
  
  *:focus {
    outline-color: var(--focus-color);
  }
  
  button:focus,
  input:focus,
  select:focus,
  textarea:focus {
    box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.2);
  }
}

/* Animation preferences */
@media (prefers-reduced-motion: no-preference) {
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }
  
  .animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure sufficient spacing for readability */
p, li, dd {
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.3;
  margin-bottom: 0.5em;
}

/* Improve readability for dyslexia */
.dyslexia-friendly {
  font-family: 'OpenDyslexic', 'Comic Sans MS', sans-serif;
  letter-spacing: 0.05em;
  word-spacing: 0.1em;
  line-height: 1.8;
}

/* Status indicators with text alternatives */
.status-indicator::before {
  content: attr(data-status);
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Ensure form labels are properly associated */
label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

/* Required field indicators */
.required::after {
  content: " *";
  color: #dc2626;
  font-weight: bold;
}

.high-contrast .required::after {
  color: #000000 !important;
}
